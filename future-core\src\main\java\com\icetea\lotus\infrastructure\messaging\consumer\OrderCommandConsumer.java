package com.icetea.lotus.infrastructure.messaging.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.core.domain.entity.Order;
import com.icetea.lotus.core.domain.entity.Trade;
import com.icetea.lotus.core.domain.service.OrderMatchingEngineService;
import com.icetea.lotus.infrastructure.messaging.command.OrderCommand;
import com.icetea.lotus.infrastructure.messaging.producer.OrderEventProducer;
import com.icetea.lotus.infrastructure.sharding.SymbolShardingManager;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Consumer cho OrderCommand
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderCommandConsumer {

    private final SymbolShardingManager shardingManager;
    private final OrderMatchingEngineService orderMatchingEngineService;
    private final OrderEventProducer orderEventProducer;
    private final ObjectMapper objectMapper;
    @Value("${topic-kafka.contract.order-commands}")
    private String orderCommandsTopic;

    private final KafkaTemplate<String, String> kafkaTemplate;
    /**
     * Xử lý OrderCommand
     * Note: Disabled by default để tránh conflict với matching-engine OrderCommandConsumer
     * Enable bằng cách set future-core.order-command-consumer.enabled=true
     */
    @KafkaListener(
        topics = "${topic-kafka.contract.order-commands}",
        containerFactory = "kafkaListenerContainerFactory",
        groupId = "${spring.kafka.consumer.group-id}"
    )
    @SneakyThrows(JsonProcessingException.class)
    public void handleOrderCommand(String message){
        log.info("handleOrderCommand Bắt đầu xử lý command từ topic {}, data = {}", orderCommandsTopic, message);
        OrderCommand command = objectMapper.readValue(message, OrderCommand.class);
        log.info("handleOrderCommand after parse data = {}", command.toString());
        if (command == null || command.getOrder() == null || command.getOrder().getSymbol() == null) {
            log.error("Nhận command không hợp lệ từ topic {}, command = {}", orderCommandsTopic, command);
            return;
        }

        log.info("Nhận command từ topic {}, type = {}, symbol = {}, orderId = {}",
                orderCommandsTopic, command.getType(), command.getOrder().getSymbol().getValue(), command.getOrder().getOrderId());

        String symbol = command.getOrder().getSymbol().getValue();
        log.info("handleOrderCommand Symbol: {}", symbol);

        // Chỉ xử lý command nếu symbol được gán cho instance này
        if (!shardingManager.isSymbolOwnedByThisPod(symbol)) {
            log.debug("Bỏ qua command: {}, symbol: {} không được gán cho pod này", command.getType(), symbol);
            return;
        }

        // Xử lý command theo loại
        log.info("handleOrderCommand Phân loại message theo type {}", command.getType());
        switch (command.getType()) {
            case PLACE_ORDER:
                log.debug("Xử lý command PLACE_ORDER, symbol: {}", symbol);
                handlePlaceOrderCommand(command.getOrder());
                break;

            case CANCEL_ORDER:
                log.debug("Xử lý command CANCEL_ORDER, symbol: {}", symbol);
                handleCancelOrderCommand(command.getOrder());
                break;

            case UPDATE_ORDER:
                log.debug("Xử lý command UPDATE_ORDER, symbol: {}", symbol);
                handleUpdateOrderCommand(command.getOrder());
                break;

            default:
                log.warn("Không hỗ trợ loại command: {}", command.getType());
                break;
        }
        log.info("END handleOrderCommand, symbol: {}, command = {}", symbol, command.getCommandId());
    }

    @KafkaListener(topics = "action_push", groupId = "test-consumer-group", containerFactory = "kafkaListenerContainerFactory")
    public void pushMessage(String message) throws JsonProcessingException {
        log.info("Test message received: {}", message.toString());
        if(message != null){
            OrderCommand orderCommand = new OrderCommand();
            orderCommand.setCommandId("123456");
            kafkaTemplate.send("test-consumer-local", objectMapper.writeValueAsString(orderCommand));
        }
    }


    @KafkaListener(topics = "test-consumer-local", groupId = "test-consumer-group", containerFactory = "kafkaListenerContainerFactory")
    public void testMessage(String message) throws JsonProcessingException {
        log.info("Test message received: {}", message.toString());
        OrderCommand orderCommand = objectMapper.readValue(message, OrderCommand.class);
        log.info("Test message received parse: {}",orderCommand.toString());
    }

    /**
     * Xử lý command đặt lệnh
     *
     * @param order Lệnh
     */
    private void handlePlaceOrderCommand(Order order) {
        try {
            // Xử lý lệnh bằng OrderMatchingEngineService
            List<Trade> trades = orderMatchingEngineService.processOrder(order);
            log.info("handlePlaceOrderCommand: {}", trades.toString());
            // Gửi event
            orderEventProducer.publishOrderPlacedEvent(order, trades);
        } catch (Exception e) {
            log.error("Lỗi khi xử lý command đặt lệnh: {}", order.getOrderId(), e);
        }
    }

    /**
     * Xử lý command hủy lệnh
     *
     * @param order Lệnh
     */
    private void handleCancelOrderCommand(Order order) {
        try {
            // Hủy lệnh bằng OrderMatchingEngineService
            boolean success = orderMatchingEngineService.cancelOrder(order);

            if (success) {
                // Gửi event
                orderEventProducer.publishOrderCancelledEvent(order);
            }
        } catch (Exception e) {
            log.error("Lỗi khi xử lý command hủy lệnh: {}", order.getOrderId(), e);
        }
    }

    /**
     * Xử lý command cập nhật lệnh
     *
     * @param order Lệnh
     */
    private void handleUpdateOrderCommand(Order order) {
        try {
            // Hiện tại OrderMatchingEngineService không có phương thức updateOrder
            // Nên chúng ta sẽ hủy lệnh cũ và đặt lệnh mới
            boolean cancelSuccess = orderMatchingEngineService.cancelOrder(order);

            if (cancelSuccess) {
                // Đặt lệnh mới
                orderMatchingEngineService.processOrder(order);

                // Gửi event
                orderEventProducer.publishOrderUpdatedEvent(order);
            }
        } catch (Exception e) {
            log.error("Lỗi khi xử lý command cập nhật lệnh: {}", order.getOrderId(), e);
        }
    }


}
