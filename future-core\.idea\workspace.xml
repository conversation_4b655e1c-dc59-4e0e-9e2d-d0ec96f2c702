<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ce32241e-1037-4dec-8e37-1c32a996c9bb" name="Changes" comment="feat: add symbol sharding configuration for future-core and enhance auto-assignment logic">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/icetea/lotus/infrastructure/messaging/consumer/OrderCommandConsumer.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/icetea/lotus/infrastructure/messaging/consumer/OrderCommandConsumer.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/icetea/lotus/infrastructure/sharding/SymbolShardingManager.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/icetea/lotus/infrastructure/sharding/SymbolShardingManager.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application.yaml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="fix_longnt" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="HARD" />
    <option name="UPDATE_TYPE" value="REBASE" />
  </component>
  <component name="GitLabMergeRequestFiltersHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPENED&quot;,
    &quot;assignee&quot;: {
      &quot;type&quot;: &quot;org.jetbrains.plugins.gitlab.mergerequest.ui.filters.GitLabMergeRequestsFiltersValue.MergeRequestsMemberFilterValue.MergeRequestsAssigneeFilterValue&quot;,
      &quot;username&quot;: &quot;edwardnguyen98&quot;,
      &quot;fullname&quot;: &quot;edwardnguyen&quot;
    }
  }
}</component>
  <component name="GitLabMergeRequestsSettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;first&quot;: &quot;https://gitlab.com/trading-platform7362712/be/future-core.git&quot;,
    &quot;second&quot;: &quot;3ac13dfb-049e-45f6-85ed-3cb4259e8e14&quot;
  }
}</component>
  <component name="GitRewordedCommitMessages">
    <option name="commitMessagesMapping">
      <RewordedCommitMessageMapping>
        <option name="originalMessage" value="feat: refactor OrderCommandProducer to use traditional routing and enhance error handling" />
        <option name="rewordedMessage" value="feat: remove conditional property for OrderCommandConsumer&#10;&#10;&#10;feat: update logging levels in application.yaml and enable conditional OrderCommandConsumer&#10;&#10;&#10;feat: refactor OrderCommandProducer to use traditional routing and enhance error handling" />
      </RewordedCommitMessageMapping>
    </option>
    <option name="currentCommit" value="1" />
    <option name="onto" value="070f8cf1f8a44074fbfc00806e8fd4af5da72628" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="DEPENDENCY_CHECKER_PROBLEMS_TAB" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2wH6wGhFco23p0T68HpBlbBORI5" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.future-core [clean].executor": "Run",
    "Maven.future-core [compile].executor": "Run",
    "Maven.future-core [install].executor": "Run",
    "Notification.DisplayName-DoNotAsk-Database detector": "Database detector",
    "Notification.DisplayName-DoNotAsk-Lombok plugin": "Lombok integration problem",
    "Notification.DoNotAsk-Database detector": "true",
    "Notification.DoNotAsk-Lombok plugin": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "SONARLINT_PRECOMMIT_ANALYSIS": "true",
    "Spring Boot.FuturesCoreApplication.executor": "Debug",
    "git-widget-placeholder": "dev",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/Project/cex-be/market",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "settings.editor.selected.configurable": "File.Encoding",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunManager">
    <configuration name="FuturesCoreApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="futures-core" />
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.icetea.lotus.FuturesCoreApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26574.91" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26574.91" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ce32241e-1037-4dec-8e37-1c32a996c9bb" name="Changes" comment="" />
      <created>1745686388346</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1745686388346</updated>
      <workItem from="1745686389477" duration="1391000" />
      <workItem from="1745691530638" duration="182000" />
      <workItem from="1745721478814" duration="13442000" />
      <workItem from="1745757022329" duration="7454000" />
      <workItem from="1745844569902" duration="8891000" />
      <workItem from="1745929183365" duration="3838000" />
      <workItem from="1746267114250" duration="26000" />
      <workItem from="1746267163555" duration="7542000" />
      <workItem from="1746448523733" duration="7017000" />
      <workItem from="1746537338470" duration="304000" />
      <workItem from="1746540433801" duration="10263000" />
      <workItem from="1746623244607" duration="16553000" />
      <workItem from="1746969549888" duration="214000" />
      <workItem from="1746970537944" duration="1884000" />
      <workItem from="1747055937638" duration="13368000" />
      <workItem from="1747232628878" duration="3077000" />
      <workItem from="1747321068341" duration="162000" />
      <workItem from="1747321399820" duration="2720000" />
      <workItem from="1747325310348" duration="1895000" />
      <workItem from="1747403831400" duration="17049000" />
      <workItem from="1747745509924" duration="9218000" />
      <workItem from="1747834767911" duration="4648000" />
      <workItem from="1747845831066" duration="2617000" />
      <workItem from="1748272626452" duration="1544000" />
      <workItem from="1748962971254" duration="5177000" />
      <workItem from="1749046278756" duration="1888000" />
      <workItem from="1749052673786" duration="861000" />
      <workItem from="1749392415415" duration="1573000" />
      <workItem from="1749654585649" duration="600000" />
      <workItem from="1749917787985" duration="129000" />
      <workItem from="1749917927064" duration="81000" />
      <workItem from="1750085238979" duration="2741000" />
      <workItem from="1750164252776" duration="1221000" />
      <workItem from="1750265875959" duration="930000" />
      <workItem from="1750341382203" duration="452000" />
      <workItem from="1751385530675" duration="2467000" />
      <workItem from="1751795861299" duration="955000" />
      <workItem from="1751797379819" duration="140000" />
      <workItem from="1751906575388" duration="710000" />
      <workItem from="1751986121654" duration="9000" />
      <workItem from="1751989304469" duration="2120000" />
      <workItem from="1752074581457" duration="4410000" />
      <workItem from="1752079126970" duration="4824000" />
    </task>
    <task id="LOCAL-00001" summary="update code base">
      <option name="closed" value="true" />
      <created>1745691629491</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1745691629491</updated>
    </task>
    <task id="LOCAL-00002" summary="update code base">
      <option name="closed" value="true" />
      <created>1745736980975</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1745736980975</updated>
    </task>
    <task id="LOCAL-00003" summary="update code base">
      <option name="closed" value="true" />
      <created>1745759705896</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1745759705896</updated>
    </task>
    <task id="LOCAL-00004" summary="update code base">
      <option name="closed" value="true" />
      <created>1745761864774</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1745761864774</updated>
    </task>
    <task id="LOCAL-00005" summary="convert to clean architechture">
      <option name="closed" value="true" />
      <created>1745765830966</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1745765830966</updated>
    </task>
    <task id="LOCAL-00006" summary="fix base">
      <option name="closed" value="true" />
      <created>1745863373319</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1745863373319</updated>
    </task>
    <task id="LOCAL-00007" summary="fix base">
      <option name="closed" value="true" />
      <created>1745864212444</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1745864212444</updated>
    </task>
    <task id="LOCAL-00008" summary="fix message log">
      <option name="closed" value="true" />
      <created>1745934049178</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1745934049178</updated>
    </task>
    <task id="LOCAL-00009" summary="fix base">
      <option name="closed" value="true" />
      <created>1746296437532</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1746296437532</updated>
    </task>
    <task id="LOCAL-00010" summary="fix base">
      <option name="closed" value="true" />
      <created>1746448614581</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1746448614581</updated>
    </task>
    <task id="LOCAL-00011" summary="fix base">
      <option name="closed" value="true" />
      <created>1746467517183</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1746467517183</updated>
    </task>
    <task id="LOCAL-00012" summary="fix base">
      <option name="closed" value="true" />
      <created>1746554149189</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1746554149189</updated>
    </task>
    <task id="LOCAL-00013" summary="fix base">
      <option name="closed" value="true" />
      <created>1746556486777</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1746556486777</updated>
    </task>
    <task id="LOCAL-00014" summary="fix base">
      <option name="closed" value="true" />
      <created>1746639635195</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1746639635195</updated>
    </task>
    <task id="LOCAL-00015" summary="fix base">
      <option name="closed" value="true" />
      <created>1746972348038</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1746972348038</updated>
    </task>
    <task id="LOCAL-00016" summary="fix base">
      <option name="closed" value="true" />
      <created>1747069620875</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1747069620875</updated>
    </task>
    <task id="LOCAL-00017" summary="fix KLineGeneratorJob">
      <option name="closed" value="true" />
      <created>1747235719000</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1747235719000</updated>
    </task>
    <task id="LOCAL-00018" summary="feat: implement manual Redis data handling and disable application caching">
      <option name="closed" value="true" />
      <created>1747322996184</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1747322996184</updated>
    </task>
    <task id="LOCAL-00019" summary="feat: add Snowflake ID generator configuration and update order ID generation">
      <option name="closed" value="true" />
      <created>1747326116368</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1747326116368</updated>
    </task>
    <task id="LOCAL-00020" summary="feat: implement TWAP order matching algorithm and refactor order processing logic">
      <option name="closed" value="true" />
      <created>1747409610907</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1747409610907</updated>
    </task>
    <task id="LOCAL-00021" summary="feat: add liquidation configuration and implement financial record handling">
      <option name="closed" value="true" />
      <created>1747420024064</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1747420024064</updated>
    </task>
    <task id="LOCAL-00022" summary="feat: implement close position order creation and update position handling">
      <option name="closed" value="true" />
      <created>1747421604338</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1747421604338</updated>
    </task>
    <task id="LOCAL-00023" summary="feat: implement KLine caching and add LastPriceDto for WebSocket updates">
      <option name="closed" value="true" />
      <created>1747752861578</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1747752861578</updated>
    </task>
    <task id="LOCAL-00024" summary="feat: add event publishing for significant OrderBook changes and enhance OrderBook comparison logic">
      <option name="closed" value="true" />
      <created>1747776624508</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1747776624508</updated>
    </task>
    <task id="LOCAL-00025" summary="feat: add break even price calculation for positions and enhance logging in order event handling">
      <option name="closed" value="true" />
      <created>1747837417447</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1747837417447</updated>
    </task>
    <task id="LOCAL-00026" summary="feat: simplify position update logic and enhance logging for entry and break even price calculations">
      <option name="closed" value="true" />
      <created>1747838446203</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1747838446203</updated>
    </task>
    <task id="LOCAL-00027" summary="feat: enable real-time K-line updates and add save functionality for existing K-lines">
      <option name="closed" value="true" />
      <created>1747847681801</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1747847681801</updated>
    </task>
    <task id="LOCAL-00028" summary="feat: add support for multiple real-time K-line update periods and enhance update logic">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00029" summary="feat: implement intelligent sharding configuration and update order routing logic">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00030" summary="update Redis configuration to use service host and default port">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00031" summary="feat: update logging levels in application.yaml and enable conditional OrderCommandConsumer">
      <option name="closed" value="true" />
      <created>*************</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>*************</updated>
    </task>
    <task id="LOCAL-00032" summary="feat: remove conditional property for OrderCommandConsumer">
      <option name="closed" value="true" />
      <created>1752079798203</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1752079798203</updated>
    </task>
    <task id="LOCAL-00033" summary="feat: add symbol sharding configuration for future-core and enhance auto-assignment logic">
      <option name="closed" value="true" />
      <created>1752083544918</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1752083544918</updated>
    </task>
    <option name="localTasksCounter" value="34" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="java:org.springframework.security:spring-security-core" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-core" />
    <option featureType="dependencySupport" implementationName="java:jakarta.validation:jakarta.validation-api" />
    <option featureType="dependencySupport" implementationName="java:io.projectreactor:reactor-core" />
    <option featureType="dependencySupport" implementationName="java:javax.persistence:javax.persistence-api" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.data:spring-data-commons" />
    <option featureType="dependencySupport" implementationName="executable:kubectl" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.cloud:spring-cloud-context" />
    <option featureType="dependencySupport" implementationName="java:org.hibernate.validator:hibernate-validator" />
    <option featureType="dependencySupport" implementationName="java:javax.validation:validation-api" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-webmvc" />
    <option featureType="dependencySupport" implementationName="java:org.projectlombok:lombok" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-websocket" />
    <option featureType="dependencySupport" implementationName="executable:docker" />
    <option featureType="dependencySupport" implementationName="java:jakarta.persistence:jakarta.persistence-api" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.batch:spring-batch-core" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-messaging" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.boot:spring-boot" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="30973c6f-03d4-4d54-8084-f4c87b7f7d28" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="30973c6f-03d4-4d54-8084-f4c87b7f7d28">
          <value>
            <State />
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="dev" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="update code base" />
    <MESSAGE value="convert to clean architechture" />
    <MESSAGE value="fix message log" />
    <MESSAGE value="fix base" />
    <MESSAGE value="fix KLineGeneratorJob" />
    <MESSAGE value="feat: implement manual Redis data handling and disable application caching" />
    <MESSAGE value="feat: add Snowflake ID generator configuration and update order ID generation" />
    <MESSAGE value="feat: implement TWAP order matching algorithm and refactor order processing logic" />
    <MESSAGE value="feat: add liquidation configuration and enhance position management methods" />
    <MESSAGE value="feat: add liquidation configuration and implement financial record handling" />
    <MESSAGE value="feat: implement close position order creation and update position handling" />
    <MESSAGE value="feat: implement KLine caching and update last price handling in WebSocket" />
    <MESSAGE value="feat: implement KLine caching and add LastPriceDto for WebSocket updates" />
    <MESSAGE value="feat: add event publishing for significant OrderBook changes and enhance OrderBook comparison logic" />
    <MESSAGE value="feat: implement OrderBook delta updates with throttling and WebSocket integration" />
    <MESSAGE value="feat: add break even price calculation for positions and enhance logging in order event handling" />
    <MESSAGE value="feat: simplify position update logic and enhance logging for entry and break even price calculations" />
    <MESSAGE value="feat: enable real-time K-line updates and add save functionality for existing K-lines" />
    <MESSAGE value="feat: add support for multiple real-time K-line update periods and enhance update logic" />
    <MESSAGE value="feat: implement intelligent sharding configuration and update order routing logic" />
    <MESSAGE value="update Redis configuration to use service host and default port" />
    <MESSAGE value="feat: refactor OrderCommandProducer to use traditional routing and enhance error handling" />
    <MESSAGE value="feat: update logging levels in application.yaml and enable conditional OrderCommandConsumer" />
    <MESSAGE value="feat: remove conditional property for OrderCommandConsumer" />
    <MESSAGE value="feat: add symbol sharding configuration for future-core and enhance auto-assignment logic" />
    <option name="LAST_COMMIT_MESSAGE" value="feat: add symbol sharding configuration for future-core and enhance auto-assignment logic" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>