package com.icetea.lotus.job;

import com.icetea.lotus.processor.CoinProcessorFactory;
import com.icetea.lotus.service.KlineRobotMarketService;
import com.icetea.lotus.util.DateUtil;
import com.icetea.lotus.util.WebSocketConnectionManage;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Calendar;

/**
 * Generate K-line information for each time period
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class KLineGeneratorJob {

    public static String[] PERIOD = {"1min", "5min", "15min", "30min", "60min", "4hour", "1day", "1mon", "1week"};
    private final CoinProcessorFactory processorFactory;
    @Qualifier("lotusTaskExecutor")
    private final TaskExecutor taskExecutor;
    private final KlineRobotMarketService klineRobotMarketService;

    @NotNull
    private static Calendar getCalendarThenLog(String s) {
        Calendar calendar = Calendar.getInstance();
        log.info(s, calendar.getTime());
        return calendar;
    }

    /**
     * Minute timer, processing minute K-line
     * Fixed: Generate K-lines for completed periods, not current period
     */
    @Scheduled(cron = "0 * * * * *")
    public void handle5minKLine() {
        Calendar calendar = getCalendarThenLog("Minute K-line:{}");
        //    Set the seconds and microsecond fields to 0
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        long currentTime = calendar.getTimeInMillis();

        // Move to previous minute to generate completed K-line
        calendar.add(Calendar.MINUTE, -1);
        long completedTime = calendar.getTimeInMillis();
        int minute = calendar.get(Calendar.MINUTE);
        int hour = calendar.get(Calendar.HOUR_OF_DAY);

        processorFactory.getProcessorMap().forEach((symbol, processor) -> {
            if (!processor.isStopKline()) {
                taskExecutor.execute(new Runnable() {
                    @Override
                    public void run() {
                        // Generate 1-minute K-line for completed minute
                        processor.generateKLine(1, Calendar.MINUTE, completedTime);

                        // Generate other time frames if conditions are met
                        if ((minute + 1) % 5 == 0) {
                            processor.generateKLine(5, Calendar.MINUTE, completedTime);
                        }
                        if ((minute + 1) % 10 == 0) {
                            processor.generateKLine(10, Calendar.MINUTE, completedTime);
                        }
                        if ((minute + 1) % 15 == 0) {
                            processor.generateKLine(15, Calendar.MINUTE, completedTime);
                        }
                        if ((minute + 1) % 30 == 0) {
                            processor.generateKLine(30, Calendar.MINUTE, completedTime);
                        }

                        // Update 24H volume
                        processor.update24HVolume(currentTime);

                        // Reset thumb at midnight
                        if (hour == 23 && minute == 59) {
                            processor.resetThumb();
                        }
                    }
                });
            }
        });
    }

    /**
     * Run every hour
     */
    @Scheduled(cron = "0 0 * * * *")
    public void handleHourKLine() {
        processorFactory.getProcessorMap().forEach((symbol, processor) -> {
            if (!processor.isStopKline()) {
                Calendar calendar = getCalendarThenLog("Hour K-line:{}");
                //    Set the seconds and microsecond fields to 0
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                long time = calendar.getTimeInMillis();

                processor.generateKLine(1, Calendar.HOUR_OF_DAY, time);
            }
        });
    }

    /**
     * Run every 4 hours
     */
    @Scheduled(cron = "0 0 0,4,8,12,16,20 * * *")
    public void handleHourKLine4Hour() {
        processorFactory.getProcessorMap().forEach((symbol, processor) -> {
            if (!processor.isStopKline()) {
                Calendar calendar = getCalendarThenLog("Hour K-line:{}");
                //    Set the seconds and microsecond fields to 0
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                long time = calendar.getTimeInMillis();

                processor.generateKLine(4, Calendar.HOUR_OF_DAY, time);
            }
        });
    }

    /**
     * Daily 0 o'clock processor, processing daily K-line
     */
    @Scheduled(cron = "0 0 0 * * *")
    public void handleDayKLine() {
        processorFactory.getProcessorMap().forEach((symbol, processor) -> {
            if (!processor.isStopKline()) {
                Calendar calendar = getCalendarThenLog("Daily K-line:{}");
                //    Set the seconds and microsecond fields to 0
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                long time = calendar.getTimeInMillis();
                int week = calendar.get(Calendar.DAY_OF_WEEK);
                int dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);
                if (week == 1) {
                    processor.generateKLine(1, Calendar.WEEK_OF_MONTH, time);
                }
                if (dayOfMonth == 1) {
                    processor.generateKLine(1, Calendar.MONTH, time);
                }
                processor.generateKLine(1, Calendar.DAY_OF_YEAR, time);
            }
        });
    }

//	@Scheduled(cron = "0 */2 * * * *")
//	public void handleTestKLine(){
//		processorFactory.getProcessorMap().forEach((symbol,processor)->{
//			if(!processor.isStopKline() && !symbol.equals("ETH/USDT")) {
//
//				Calendar calendar = Calendar.getInstance();
//				calendar.setTimeInMillis(1648915200000L);
//				for (int i = 0; i < 26; i++) {
//					long time = calendar.getTimeInMillis();
//					processor.generateKLine(1, Calendar.WEEK_OF_MONTH, time);
//					calendar.add(Calendar.WEEK_OF_MONTH,1);
//				}
//			}
//		});
//
//
//	}

    /**
     * Minute timer, processing minute K-line
     */
//    @Scheduled(cron = "5 */1 * * * ?")
//    public void handle5minKLine() {
//        processorFactory.getProcessorMap().forEach((symbol, processor) -> {
//            if (!processor.isStopKline()) {
//                taskExecutor.execute(new Runnable() {
//                    @Override
//                    public void run() {
//                        syncKLine(symbol);
//                    }
//                });
//            }
//        });
//    }
    public void syncKLine(String symbol) {

        // List<Symbol> symbols = klineRobotMarketService.findAllSymbol();

        // Get the current time (seconds)
        long currentTime = DateUtil.getTimeMillis() / 1000;
        // Initialize the K-line, time point
        // int count = 2000;
        log.info("Execute to obtain the K-line in minutes [Start]");
        for (String period : PERIOD) {
            // long fromTime = 0;
            long fromTime = klineRobotMarketService.findMaxTimestamp(symbol, period); //    +1 is to not get the last K-line obtained last time
            if (fromTime <= 1) {
                fromTime = 0;
            } else {
                fromTime = fromTime / 1000;
            }
            long timeGap = currentTime - fromTime;
            log.info("{} Minute K-line currentTime:{},fromTime:{},timeGap:{}", symbol, currentTime, fromTime, timeGap);
            if (period.equals("1min") && timeGap >= 60) { //    超出1分钟
                if (fromTime == 0) {
                    //   logger.info("Execute to obtain K-line in minutes[1min] ===> from == 0");
                    // Initialize the K-line to obtain the last 600 K-lines
                    WebSocketConnectionManage.getWebSocket().reqKLineList(symbol, period, currentTime - 60 * 2500, currentTime);
                } else {
                    // Non-initialization, get the recently generated K-line
                    long toTime = fromTime + (timeGap / 60) * 60 - 5;//   timeGap - (timeGap % 60); //    +10 seconds is to obtain the K-line in this range
                    WebSocketConnectionManage.getWebSocket().reqKLineList(symbol, period, fromTime, toTime);
                }
            }

            if (period.equals("5min") && timeGap >= 60 * 5) { //    More than 5 minutes
                if (fromTime == 0) {
                    // Initialize the K-line to obtain the last 600 K-lines
                    WebSocketConnectionManage.getWebSocket().reqKLineList(symbol, period, currentTime - 5 * 60 * 1000, currentTime);
                } else {
                    // Non-initialization, get the recently generated K-line
                    long toTime = fromTime + (timeGap / (60 * 5)) * (60 * 5) - 5;//   timeGap - (timeGap % 60); //   +10 seconds is to obtain the K-line in this range
                    WebSocketConnectionManage.getWebSocket().reqKLineList(symbol, period, fromTime, toTime);
                }
            }

            if (period.equals("15min") && timeGap >= (60 * 15)) { //    More than 15 minutes
                if (fromTime == 0) {
                    // Initialize the K-line to obtain the last 600 K-lines
                    WebSocketConnectionManage.getWebSocket().reqKLineList(symbol, period, currentTime - 15 * 60 * 1000, currentTime);
                } else {
                    // Non-initialization, get the recently generated K-line
                    long toTime = fromTime + (timeGap / (60 * 15)) * (60 * 15) - 5;//   timeGap - (timeGap % 60); //   +10 seconds is to obtain the K-line in this range
                    WebSocketConnectionManage.getWebSocket().reqKLineList(symbol, period, fromTime, toTime);
                }
            }

            if (period.equals("30min") && timeGap >= (60 * 30)) { //    More than 30 minutes
                if (fromTime == 0) {
                    // Initialize the K-line to obtain the last 600 K-lines
                    WebSocketConnectionManage.getWebSocket().reqKLineList(symbol, period, currentTime - 30 * 60 * 1000, currentTime);
                } else {
                    long toTime = fromTime + (timeGap / (60 * 30)) * (60 * 30) - 5;//   timeGap - (timeGap % 60); //    +10 seconds is to obtain the K-line in this range
                    WebSocketConnectionManage.getWebSocket().reqKLineList(symbol, period, fromTime, toTime);
                }
            }

            if (period.equals("60min") && timeGap >= (60 * 60)) { //    More than 60 minutes
                if (fromTime == 0) {
                    // Initialize the K-line to obtain the last 600 K-lines
                    WebSocketConnectionManage.getWebSocket().reqKLineList(symbol, period, currentTime - 60 * 60 * 1000, currentTime);
                } else {
                    long toTime = fromTime + (timeGap / (60 * 60)) * (60 * 60) - 5;//   timeGap - (timeGap % 60); //   +10 seconds is to obtain the K-line in this range
                    WebSocketConnectionManage.getWebSocket().reqKLineList(symbol, period, fromTime, toTime);
                }
            }

            if (period.equals("4hour") && timeGap >= (60 * 60 * 4)) { //    More than 4 hours
                if (fromTime == 0) {
                    // Initialize the K-line to obtain the last 600 K-lines
                    WebSocketConnectionManage.getWebSocket().reqKLineList(symbol, period, currentTime - 4 * 60 * 60 * 600, currentTime);
                } else {
                    long toTime = fromTime + (timeGap / (60 * 60 * 4)) * (60 * 60 * 4) - 5;//   timeGap - (timeGap % 60); //    +10 seconds is to obtain the K-line in this range
                    WebSocketConnectionManage.getWebSocket().reqKLineList(symbol, period, fromTime, toTime);
                }
            }

            if (period.equals("1day") && timeGap >= (60 * 60 * 24)) { //    More than 24 hours
                if (fromTime == 0) {
                    // Initialize the K-line to obtain the last 600 K-lines
                    WebSocketConnectionManage.getWebSocket().reqKLineList(symbol, period, currentTime - 24 * 60 * 60 * 1000, currentTime);
                } else {
                    long toTime = fromTime + (timeGap / (60 * 60 * 24)) * (60 * 60 * 24) - 5;//   timeGap - (timeGap % 60); //    +10 seconds is to obtain the K-line in this range
                    WebSocketConnectionManage.getWebSocket().reqKLineList(symbol, period, fromTime, toTime);
                }
            }

            if (period.equals("1week") && timeGap >= (60 * 60 * 24 * 7)) { //    More than 24 hours
                if (fromTime == 0) {
                    // Initialize the K-line to obtain the last 600 K-lines
                    WebSocketConnectionManage.getWebSocket().reqKLineList(symbol, period, currentTime - 7 * 24 * 60 * 60 * 500, currentTime);
                } else {
                    long toTime = fromTime + (timeGap / (60 * 60 * 24 * 7)) * (60 * 60 * 24 * 7) - 5;//   timeGap - (timeGap % 60); //    +10 seconds is to obtain the K-line in this range
                    WebSocketConnectionManage.getWebSocket().reqKLineList(symbol, period, fromTime, toTime);
                }
            }

            if (period.equals("1mon") && timeGap >= (60 * 60 * 24 * 30)) { //    More than 24 hours
                if (fromTime == 0) {
                    // Initialize the K-line to obtain the last 600 K-lines
                    WebSocketConnectionManage.getWebSocket().reqKLineList(symbol, period, currentTime - 30 * 24 * 60 * 60 * 100, currentTime);
                } else {
                    long toTime = fromTime + (timeGap / (60 * 60 * 24 * 30)) * (60 * 60 * 24 * 30) - 5;//   timeGap - (timeGap % 60); //    +10 seconds is to obtain the K-line in this range
                    WebSocketConnectionManage.getWebSocket().reqKLineList(symbol, period, fromTime, toTime);
                }
            }
        }
    }
}
