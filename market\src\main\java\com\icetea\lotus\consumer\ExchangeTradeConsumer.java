package com.icetea.lotus.consumer;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.entity.ExchangeOrder;
import com.icetea.lotus.entity.ExchangeTrade;
import com.icetea.lotus.entity.TradePlate;
import com.icetea.lotus.job.ExchangePushJob;
import com.icetea.lotus.processor.CoinProcessor;
import com.icetea.lotus.processor.CoinProcessorFactory;
import com.icetea.lotus.service.ExchangeOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@RequiredArgsConstructor
@Component
public class ExchangeTradeConsumer {
    private final Logger logger = LoggerFactory.getLogger(ExchangeTradeConsumer.class);
    private final CoinProcessorFactory coinProcessorFactory;
    private final SimpMessagingTemplate messagingTemplate;
    private final ExchangeOrderService exchangeOrderService;
    private final ExecutorService executor =
            new ThreadPoolExecutor(30, 100, 0L, TimeUnit.MILLISECONDS,
                    new LinkedBlockingQueue<>(1024), new ThreadPoolExecutor.AbortPolicy());
    private final ExchangePushJob pushJob;
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Value("${second.referrer.award}")
    private boolean secondReferrerAward;

    /**
     * Handle transaction details
     */
    @KafkaListener(topics = "${topic-kafka.exchange.trade}", containerFactory = "kafkaListenerContainerFactory")
    public void handleTrade(List<ConsumerRecord<String, String>> records) {
        for (ConsumerRecord<String, String> record : records) {
            executor.submit(new HandleTradeThread(record));
        }
    }

    @KafkaListener(topics = "${topic-kafka.exchange.order-completed}", containerFactory = "kafkaListenerContainerFactory")
    public void handleOrderCompleted(List<ConsumerRecord<String, String>> records) {
        try {
            for (ConsumerRecord<String, String> record : records) {
                // logger.info("Order transaction processing completion message topic={},value={}", record.topic(), record.value());
                List<ExchangeOrder> orders = objectMapper.readValue(record.value(), new TypeReference<List<ExchangeOrder>>() {
                });
                for (ExchangeOrder order : orders) {
                    String symbol = order.getSymbol();
                    log.info("Order completion：{}", order);
                    //  Entrusted transaction completion processing
                    exchangeOrderService.tradeCompleted(order.getOrderId(), order.getTradedAmount(), order.getTurnover());
                    //  Push order delivery
                    messagingTemplate.convertAndSend("/topic/market/order-completed/" + symbol + "/" + order.getMemberId(), order);
                }
            }
        } catch (Exception e) {
            log.info("====Order completion exception, error={}", e.getMessage());
        }
    }

    /**
     * Consumer trading information
     */
    @KafkaListener(topics = "${topic-kafka.exchange.trade-plate}", containerFactory = "kafkaListenerContainerFactory")
    public void handleTradePlate(List<ConsumerRecord<String, String>> records) {
        try {
            for (ConsumerRecord<String, String> record : records) {
                logger.info("Push the topic information={},value={},size={}", record.topic(), record.value(), records.size());
//                TradePlate plate = objectMapper.readValue(record.value(), TradePlate.class);
                TradePlate plate = JSON.parseObject(record.value(), TradePlate.class);

                String symbol = plate.getSymbol();
                pushJob.addPlates(symbol, plate);
                pushJob.pushTrade();
            }
        } catch (Exception e) {
            log.info("====Trade plate exception, error={}", e.getMessage());
        }
    }

    /**
     * Order cancellation successfully
     */
    @KafkaListener(topics = "${topic-kafka.exchange.order-cancel-success}", containerFactory = "kafkaListenerContainerFactory")
    public void handleOrderCanceled(List<ConsumerRecord<String, String>> records) {
        try {
            for (ConsumerRecord<String, String> record : records) {
                // logger.info("Cancel order message topic={},value={},size={}", record.topic(), record.value(), records.size());
                ExchangeOrder order = objectMapper.readValue(record.value(), ExchangeOrder.class);
                String symbol = order.getSymbol();
                //  Calling service processing
                exchangeOrderService.cancelOrder(order.getOrderId(), order.getTradedAmount(), order.getTurnover());
                //  Push real-time transactions
                messagingTemplate.convertAndSend("/topic/market/order-canceled/" + symbol + "/" + order.getMemberId(), order);
            }
        } catch (Exception e) {
            log.info("Get issue on consume Kafka topic | exchange-order-cancel-success : {}", e.getMessage());
        }
    }

    public class HandleTradeThread implements Runnable {
        private final ConsumerRecord<String, String> record;

        public HandleTradeThread(ConsumerRecord<String, String> record) {
            this.record = record;
        }

        @Override
        public void run() {
            try {
                List<ExchangeTrade> trades = objectMapper.readValue(record.value(), new TypeReference<List<ExchangeTrade>>() {
                });
                String symbol = trades.get(0).getSymbol();
                CoinProcessor coinProcessor = coinProcessorFactory.getProcessor(symbol);
                for (ExchangeTrade trade : trades) {
                    //  Details of transaction processing
                    exchangeOrderService.processExchangeTrade(trade, secondReferrerAward);
                    //  Push order transaction subscription
                    ExchangeOrder buyOrder = exchangeOrderService.findOne(trade.getBuyOrderId());
                    ExchangeOrder sellOrder = exchangeOrderService.findOne(trade.getSellOrderId());
                    messagingTemplate.convertAndSend(
                            "/topic/market/order-trade/" + symbol + "/" + buyOrder.getMemberId(), buyOrder);
                    messagingTemplate.convertAndSend(
                            "/topic/market/order-trade/" + symbol + "/" + sellOrder.getMemberId(), sellOrder);
                }
                //  Handle the K-line market
                if (coinProcessor != null) {
                    coinProcessor.process(trades);

                    // Note: K-line generation is handled by scheduled job (KLineGeneratorJob)
                    // Real-time K-line generation here can cause data inconsistency
                    // Removed real-time generation to prevent duplicate/incorrect K-lines
                }
                pushJob.addTrades(symbol, trades);
                pushJob.pushTrade();
            } catch (Exception e) {
                log.info("====Trade thread exception, record={},error={}", record, e.getMessage());
            }
        }
    }
}
