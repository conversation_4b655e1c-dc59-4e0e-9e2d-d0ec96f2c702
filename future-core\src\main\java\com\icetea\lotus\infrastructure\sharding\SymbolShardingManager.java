package com.icetea.lotus.infrastructure.sharding;

import com.icetea.lotus.core.domain.valueobject.Symbol;
import com.icetea.lotus.core.common.LogMessages;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;
import java.util.List;
import java.util.Arrays;
import java.util.ArrayList;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentSkipListMap;
import java.util.concurrent.TimeUnit;

/**
 * Quản lý phân phối các symbol giữa các instance
 * Legacy component - được giữ lại để tương thích với hệ thống cũ
 * Sử dụng SmartShardingManager cho logic mới
 */
@Slf4j
@Component
public class SymbolShardingManager {

    private final RedissonClient redissonClient;

    // Constants
    private static final String SYMBOL_TO_POD_MAP = "symbol-to-pod-map";
    private static final String SYMBOL_REBALANCING_LOCK = "symbol-rebalancing-lock";

    // Tên của pod
    @Getter
    private final String podName;

    // Cấu hình symbols cho pod này
    @Value("${future.sharding.pod-symbols:BTC/USDT,ETH/USDT,BNB/USDT,ADA/USDT,XRP/USDT,SOL/USDT,DOT/USDT,DOGE/USDT,AVAX/USDT,MATIC/USDT,LINK/USDT,UNI/USDT,LTC/USDT,EOS/ETH,BZB/USDT,HT/USDT}")
    private String podSymbolsConfig;

    // Tự động gán symbols từ cấu hình khi không có trong Redis
    @Value("${future.sharding.auto-assign-from-config:true}")
    private boolean autoAssignFromConfig;

    // OPTIMIZED: ConcurrentSkipListMap cho better cache locality và ordered access
    private final ConcurrentSkipListMap<String, Boolean> symbolOwnershipCache = new ConcurrentSkipListMap<>();

    // OPTIMIZED: ConcurrentSkipListMap cho consistent performance
    private final ConcurrentSkipListMap<String, Long> symbolOrderCountMap = new ConcurrentSkipListMap<>();

    // OPTIMIZED: Cache với TTL để reduce Redis calls
    private final ConcurrentSkipListMap<String, CacheEntry> symbolCacheWithTTL = new ConcurrentSkipListMap<>();
    private static final long CACHE_TTL_MS = 5000; // 5 seconds TTL

    // Thống kê khối lượng giao dịch cho mỗi symbol
    private final Map<String, Double> symbolVolumeMap = new ConcurrentHashMap<>();

    /**
     * Khởi tạo SymbolShardingManager
     * @param redissonClient RedissonClient
     * @param podName Tên của pod
     */
    public SymbolShardingManager(RedissonClient redissonClient,
                                @Value("${HOSTNAME}") String podName) {

        this.redissonClient = redissonClient;
        // Sử dụng giá trị từ cấu hình HOSTNAME
        this.podName = podName;

        log.info(LogMessages.ShardingManager.INFO_INITIALIZED(), this.podName);
    }

    /**
     * Kiểm tra xem symbol có được gán cho pod này không
     * @param symbol Symbol cần kiểm tra
     * @return true nếu symbol được gán cho pod này, false nếu không
     */
    public boolean isSymbolOwnedByThisPod(Symbol symbol) {
        return isSymbolOwnedByThisPod(symbol.getValue());
    }

    /**
     * Kiểm tra xem symbol có được gán cho pod này không (atomic operation)
     * @param symbolStr Symbol cần kiểm tra
     * @return true nếu symbol được gán cho pod này, false nếu không
     */
    public boolean isSymbolOwnedByThisPod(String symbolStr) {
        // Kiểm tra cache trước
        Boolean cachedResult = symbolOwnershipCache.get(symbolStr);
        if (cachedResult != null) {
            return cachedResult;
        }

        // Sử dụng Redis để lưu trữ mapping giữa symbol và pod
        RMap<String, String> symbolToPodMap = redissonClient.getMap(SYMBOL_TO_POD_MAP);

        // Kiểm tra xem symbol đã có owner chưa
        String currentOwner = symbolToPodMap.get(symbolStr);

        if (currentOwner != null) {
            // Symbol đã có owner, kiểm tra xem có phải pod này không
            boolean isOwned = currentOwner.equals(podName);
            symbolOwnershipCache.put(symbolStr, isOwned);

            if (isOwned) {
                log.debug("Pod {} đã sở hữu symbol {}", podName, symbolStr);
            } else {
                log.debug("Symbol {} đã được gán cho pod {} (pod hiện tại: {})",
                        symbolStr, currentOwner, podName);
            }

            return isOwned;
        }

        // Symbol chưa có owner, kiểm tra cấu hình trước khi gán
        if (autoAssignFromConfig) {
            List<String> configuredSymbols = parsePodSymbols();
            if (configuredSymbols.contains(symbolStr)) {
                // Symbol có trong cấu hình của pod này, thử gán
                String previousOwner = symbolToPodMap.putIfAbsent(symbolStr, podName);
                boolean isOwned = previousOwner == null || previousOwner.equals(podName);

                symbolOwnershipCache.put(symbolStr, isOwned);

                if (isOwned && previousOwner == null) {
                    log.info("Pod {} đã được gán symbol {} từ cấu hình (first assignment)", podName, symbolStr);
                } else if (isOwned) {
                    log.debug("Pod {} đã sở hữu symbol {} từ cấu hình", podName, symbolStr);
                } else {
                    log.warn("Symbol {} đã được gán cho pod {} trong khi pod {} đang cố gán từ cấu hình",
                            symbolStr, previousOwner, podName);
                }

                return isOwned;
            }
        }

        // Symbol không có trong cấu hình hoặc không bật auto-assign, không gán
        symbolOwnershipCache.put(symbolStr, false);
        log.debug("Symbol {} không có trong cấu hình của pod {} hoặc auto-assign bị tắt", symbolStr, podName);

        return false;
    }

    /**
     * Lấy tên pod sở hữu symbol
     * @param symbol Symbol cần kiểm tra
     * @return Tên pod sở hữu symbol, null nếu không có pod nào sở hữu
     */
    public String getOwnerPod(Symbol symbol) {
        return getOwnerPod(symbol.getValue());
    }

    /**
     * Lấy tên pod sở hữu symbol
     * @param symbolStr Symbol cần kiểm tra
     * @return Tên pod sở hữu symbol, null nếu không có pod nào sở hữu
     */
    public String getOwnerPod(String symbolStr) {
        RMap<String, String> symbolToPodMap = redissonClient.getMap(SYMBOL_TO_POD_MAP);
        return symbolToPodMap.get(symbolStr);
    }

    /**
     * Gán symbol cho pod này
     * @param symbol Symbol cần gán
     * @return true nếu gán thành công, false nếu không
     */
    public boolean assignSymbolToThisPod(Symbol symbol) {
        return assignSymbolToThisPod(symbol.getValue());
    }

    /**
     * Gán symbol cho pod này (force assignment)
     * @param symbolStr Symbol cần gán
     * @return true nếu gán thành công, false nếu không
     */
    public boolean assignSymbolToThisPod(String symbolStr) {
        RMap<String, String> symbolToPodMap = redissonClient.getMap(SYMBOL_TO_POD_MAP);

        // Force gán symbol cho pod này (override existing assignment)
        String previousOwner = symbolToPodMap.put(symbolStr, podName);

        // Cập nhật cache
        symbolOwnershipCache.put(symbolStr, true);

        if (previousOwner != null && !previousOwner.equals(podName)) {
            log.warn("Force assigned symbol {} from pod {} to pod {}", symbolStr, previousOwner, podName);
        } else {
            log.info("Assigned symbol {} to pod {}", symbolStr, podName);
        }

        return true;
    }

    /**
     * Thử gán symbol cho pod này (atomic, không override)
     * @param symbolStr Symbol cần gán
     * @return true nếu gán thành công, false nếu symbol đã được gán cho pod khác
     */
    public boolean tryAssignSymbolToThisPod(String symbolStr) {
        RMap<String, String> symbolToPodMap = redissonClient.getMap(SYMBOL_TO_POD_MAP);

        // Atomic operation: chỉ gán nếu chưa có owner
        String previousOwner = symbolToPodMap.putIfAbsent(symbolStr, podName);
        boolean assigned = previousOwner == null;

        if (assigned) {
            // Cập nhật cache
            symbolOwnershipCache.put(symbolStr, true);
            log.info("Successfully assigned symbol {} to pod {}", symbolStr, podName);
        } else {
            // Cập nhật cache với kết quả false
            symbolOwnershipCache.put(symbolStr, false);
            log.warn("Failed to assign symbol {} to pod {} (already owned by {})",
                    symbolStr, podName, previousOwner);
        }

        return assigned;
    }

    /**
     * Hủy gán symbol khỏi pod này
     * @param symbol Symbol cần hủy gán
     * @return true nếu hủy gán thành công, false nếu không
     */
    public boolean unassignSymbolFromThisPod(Symbol symbol) {
        return unassignSymbolFromThisPod(symbol.getValue());
    }

    /**
     * Hủy gán symbol khỏi pod này
     * @param symbolStr Symbol cần hủy gán
     * @return true nếu hủy gán thành công, false nếu không
     */
    public boolean unassignSymbolFromThisPod(String symbolStr) {
        // Kiểm tra xem symbol có được gán cho pod này không
        if (!isSymbolOwnedByThisPod(symbolStr)) {
            return false;
        }

        RMap<String, String> symbolToPodMap = redissonClient.getMap(SYMBOL_TO_POD_MAP);

        // Hủy gán symbol khỏi pod này
        symbolToPodMap.remove(symbolStr, podName);

        // Cập nhật cache
        symbolOwnershipCache.put(symbolStr, false);

        log.info(LogMessages.ShardingManager.INFO_SYMBOL_UNASSIGNED(), symbolStr, podName);

        return true;
    }

    /**
     * Lấy danh sách các symbol được gán cho pod này
     * @return Danh sách các symbol
     */
    public Set<String> getSymbolsOwnedByThisPod() {
        RMap<String, String> symbolToPodMap = redissonClient.getMap(SYMBOL_TO_POD_MAP);

        Set<String> ownedSymbols = ConcurrentHashMap.newKeySet();

        // Lọc các symbol được gán cho pod này
        for (Map.Entry<String, String> entry : symbolToPodMap.entrySet()) {
            if (entry.getValue().equals(podName)) {
                ownedSymbols.add(entry.getKey());

                // Cập nhật cache
                symbolOwnershipCache.put(entry.getKey(), true);
            }
        }

        return ownedSymbols;
    }

    /**
     * Cập nhật thống kê cho symbol
     * @param symbolStr Symbol cần cập nhật
     * @param orderCount Số lượng lệnh xử lý
     * @param volume Khối lượng giao dịch
     */
    public void updateSymbolStats(String symbolStr, long orderCount, double volume) {
        symbolOrderCountMap.put(symbolStr, symbolOrderCountMap.getOrDefault(symbolStr, 0L) + orderCount);
        symbolVolumeMap.put(symbolStr, symbolVolumeMap.getOrDefault(symbolStr, 0.0) + volume);
    }

    /**
     * Lấy số lượng lệnh xử lý cho symbol
     * @param symbolStr Symbol cần lấy thống kê
     * @return Số lượng lệnh xử lý
     */
    public long getSymbolOrderCount(String symbolStr) {
        return symbolOrderCountMap.getOrDefault(symbolStr, 0L);
    }

    /**
     * Lấy khối lượng giao dịch cho symbol
     * @param symbolStr Symbol cần lấy thống kê
     * @return Khối lượng giao dịch
     */
    public double getSymbolVolume(String symbolStr) {
        return symbolVolumeMap.getOrDefault(symbolStr, 0.0);
    }

    /**
     * Đặt lại thống kê cho symbol
     * @param symbolStr Symbol cần đặt lại thống kê
     */
    public void resetSymbolStats(String symbolStr) {
        symbolOrderCountMap.remove(symbolStr);
        symbolVolumeMap.remove(symbolStr);
    }

    /**
     * Lấy tất cả thống kê về số lượng lệnh xử lý
     * @return Map chứa thống kê số lượng lệnh xử lý cho mỗi symbol
     */
    public Map<String, Long> getAllSymbolOrderCounts() {
        return new ConcurrentHashMap<>(symbolOrderCountMap);
    }

    /**
     * Lấy tất cả thống kê về khối lượng giao dịch
     * @return Map chứa thống kê khối lượng giao dịch cho mỗi symbol
     */
    public Map<String, Double> getAllSymbolVolumes() {
        return new ConcurrentHashMap<>(symbolVolumeMap);
    }

    /**
     * Cân bằng lại các symbol giữa các pod
     * @param allSymbols Danh sách tất cả các symbol
     * @param allPods Danh sách tất cả các pod
     */
    public void rebalanceSymbols(Set<String> allSymbols, Set<String> allPods) {
        // Nếu không có pod nào, không cần cân bằng
        if (allPods.isEmpty()) {
            return;
        }

        // Lấy lock để đảm bảo chỉ có một pod thực hiện cân bằng tại một thời điểm
        RLock lock = redissonClient.getLock(SYMBOL_REBALANCING_LOCK);

        try {
            // Sử dụng quick lock cho rebalancing (200ms wait, 2000ms lease)
            if (lock.tryLock(200, 2000, TimeUnit.MILLISECONDS)) {
                try {
                    log.info(LogMessages.ShardingManager.INFO_REBALANCING_STARTED(), podName);

                    RMap<String, String> symbolToPodMap = redissonClient.getMap(SYMBOL_TO_POD_MAP);

                    // Xóa các symbol không còn tồn tại
                    for (String symbol : symbolToPodMap.keySet()) {
                        if (!allSymbols.contains(symbol)) {
                            symbolToPodMap.remove(symbol);

                            // Cập nhật cache
                            symbolOwnershipCache.remove(symbol);

                            log.info(LogMessages.ShardingManager.INFO_SYMBOL_REMOVED(), symbol);
                        }
                    }

                    // Gán các symbol chưa được gán
                    int podIndex = 0;
                    String[] podArray = allPods.toArray(new String[0]);

                    for (String symbol : allSymbols) {
                        if (!symbolToPodMap.containsKey(symbol)) {
                            String assignedPod = podArray[podIndex];
                            symbolToPodMap.put(symbol, assignedPod);

                            // Cập nhật cache nếu symbol được gán cho pod này
                            symbolOwnershipCache.put(symbol, assignedPod.equals(podName));

                            log.info(LogMessages.ShardingManager.INFO_SYMBOL_ASSIGNED(), symbol, assignedPod, null);

                            // Chuyển sang pod tiếp theo
                            podIndex = (podIndex + 1) % podArray.length;
                        }
                    }

                    log.info(LogMessages.ShardingManager.INFO_REBALANCING_COMPLETED(), podName);
                } finally {
                    lock.unlock();
                }
            } else {
                log.warn(LogMessages.ShardingManager.WARN_REBALANCING_LOCK_FAILED(), podName);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error(LogMessages.ShardingManager.ERROR_REBALANCING_INTERRUPTED(), podName, e);
        }
    }

    /**
     * Xóa cache
     */
    public void clearCache() {
        symbolOwnershipCache.clear();
        symbolCacheWithTTL.clear();
    }

    /**
     * Parse pod symbols từ cấu hình
     * @return Danh sách symbols được gán cho pod này
     */
    private List<String> parsePodSymbols() {
        if (podSymbolsConfig == null || podSymbolsConfig.trim().isEmpty()) {
            log.warn("Pod symbols config is null or empty for pod: {}", podName);
            return new ArrayList<>();
        }

        List<String> symbols = Arrays.stream(podSymbolsConfig.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .toList();

        log.debug("Parsed pod symbols for pod {}: {}", podName, symbols);
        return symbols;
    }

    /**
     * Get debug information about symbol sharding configuration
     * @return Debug information
     */
    public String getDebugInfo() {
        return String.format("Pod: %s, AutoAssign: %s, ConfiguredSymbols: %s",
                podName, autoAssignFromConfig, parsePodSymbols());
    }



    /**
     * Cache entry với TTL
     */
    private static class CacheEntry {
        private final boolean value;
        private final long timestamp;

        public CacheEntry(boolean value) {
            this.value = value;
            this.timestamp = System.currentTimeMillis();
        }

        public boolean isExpired(long ttlMs) {
            return (System.currentTimeMillis() - timestamp) > ttlMs;
        }

        public boolean getValue() {
            return value;
        }
    }
}
