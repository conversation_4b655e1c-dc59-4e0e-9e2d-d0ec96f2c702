package com.icetea.lotus.config;

import com.icetea.lotus.component.CoinExchangeRate;
import com.icetea.lotus.entity.ExchangeCoin;
import com.icetea.lotus.handler.MongoMarketHandler;
import com.icetea.lotus.handler.WebsocketMarketHandler;
import com.icetea.lotus.processor.CoinProcessor;
import com.icetea.lotus.processor.CoinProcessorFactory;
import com.icetea.lotus.processor.DefaultCoinProcessor;
import com.icetea.lotus.service.ExchangeCoinService;
import com.icetea.lotus.service.MarketService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import java.util.List;

@Configuration
@Slf4j
public class ProcessorConfig {

    @Value("${market.kline.enable-by-default:false}")
    private boolean enableKLineByDefault;

    @Bean
    public CoinProcessorFactory processorFactory(MongoMarketHandler mongoMarketHandler,
                                                 WebsocketMarketHandler wsHandler,
                                                 MarketService marketService,
                                                 CoinExchangeRate exchangeRate,
                                                 ExchangeCoinService coinService,
                                                 RestTemplate restTemplate) {

        log.info("====initialized CoinProcessorFactory start==================================");

        CoinProcessorFactory factory = new CoinProcessorFactory();
        List<ExchangeCoin> coins = coinService.findAllEnabled();
        log.info("exchange-coin result:{}", coins);

        for (ExchangeCoin coin : coins) {
            CoinProcessor processor = new DefaultCoinProcessor(coin.getSymbol(), coin.getBaseSymbol());
            processor.addHandler(mongoMarketHandler);
            processor.addHandler(wsHandler);
            processor.setMarketService(marketService);
            processor.setExchangeRate(exchangeRate);
            processor.setIsStopKLine(!enableKLineByDefault);

            if (enableKLineByDefault) {
                log.info("K-line generation enabled by default for symbol: {}", coin.getSymbol());
            }

            factory.addProcessor(coin.getSymbol(), processor);
            log.info("new processor = {}", processor);
        }

        log.info("====initialized CoinProcessorFactory completed====");
        log.info("CoinProcessorFactory = {}", factory);
        exchangeRate.setCoinProcessorFactory(factory);
        return factory;
    }
}
