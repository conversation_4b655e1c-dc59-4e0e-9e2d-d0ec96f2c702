server:
  port: 6060
  servlet:
    context-path: /future
  # <PERSON><PERSON><PERSON> hình Netty
  netty:
    connection-timeout: 3000
    max-connections: 1000
    max-idle-time: 10000
  # <PERSON><PERSON><PERSON> hình cơ sở dữ liệu

# C<PERSON>u hình Spring
spring:
  application:
    name: futures-core

  # <PERSON><PERSON><PERSON> hình bộ nhớ đệm (từ application-no-cache.yaml)
  cache:
    type: none
    jcache:
      config:
    caffeine:
      spec:

  datasource:
    url: ${DATASOURCE_URL:**********************************************}
    username: ${DATASOURCE_USERNAME:future_user}
    password: ${DATASOURCE_PASSWORD:OZrB4ysfaHJkWApoTg5EAHlbIkXYhJE97mTX70pTEL1uyEw9yNYH8MA3Gk3gRPFu}
    driver-class-name: org.postgresql.Driver
    hikari:
      # <PERSON><PERSON><PERSON> hình nhóm kết n<PERSON><PERSON> cơ bản - t<PERSON><PERSON> <PERSON>u cho thông lượng cao (từ application-optimized-db.yaml)
      minimum-idle: 15
      maximum-pool-size: 50
      pool-name: HikariCP
      schema: public

      # <PERSON><PERSON><PERSON> hình vòng đời kết nối (từ application-optimized-db.yaml)
      idle-timeout: 180000 # 3 phút
      max-lifetime: 600000 # 10 phút

      # Cấu hình lấy kết nối (từ application-optimized-db.yaml)
      connection-timeout: 30000 # 30 giây
      initialization-fail-timeout: 2000 # 2 giây

      # Cấu hình xác thực kết nối (từ application-optimized-db.yaml)
      validation-timeout: 3000 # 3 giây
      keepalive-time: 60000 # 1 phút

      # Cấu hình chất lượng kết nối (từ application-optimized-db.yaml)
      auto-commit: false
      transaction-isolation: TRANSACTION_READ_COMMITTED

      # Giám sát và gỡ lỗi (từ application-optimized-db.yaml)
      leak-detection-threshold: 30000 # 30 giây
      register-mbeans: true

      # Kiểm tra kết nối (từ application-optimized-db.yaml)
      connection-test-query: SELECT 1
      test-while-idle: true
      test-on-borrow: true

      # Khởi tạo kết nối (từ application-optimized-db.yaml)
      connection-init-sql: SET statement_timeout = 30000; SET lock_timeout = 10000;

      # Tối ưu hóa đặc biệt cho PostgreSQL (từ application-optimized-db.yaml)
      data-source-properties:
        # Lưu trữ câu lệnh
        cachePrepStmts: true
        prepStmtCacheSize: 500
        prepStmtCacheSqlLimit: 4096

        # Chuẩn bị câu lệnh phía server
        useServerPrepStmts: true

        # Xử lý hàng loạt
        rewriteBatchedStatements: true

        # Lưu trữ siêu dữ liệu
        cacheResultSetMetadata: true
        cacheServerConfiguration: true

        # Quản lý kết nối
        useLocalSessionState: true
        elideSetAutoCommits: true
        maintainTimeStats: false

        # Cấu hình mạng
        tcpKeepAlive: true
        socketTimeout: 30
        connectTimeout: 10

        # Tên ứng dụng cho giám sát
        ApplicationName: futures-core

  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: none
    database: postgresql
    show-sql: ${JPA_SHOW_SQL:false}
    open-in-view: false
    properties:
      hibernate:
        # Định dạng SQL
        format_sql: ${JPA_FORMAT_SQL:false}

        # Cấu hình JDBC (từ application-optimized-db.yaml)
        jdbc:
          lob:
            non_contextual_creation: true
          batch_size: 100
          batch_versioned_data: true
          time_zone: UTC
          # Statement timeout (in milliseconds)
          statement_timeout: 30000

        # Cấu hình phương ngữ
        dialect: org.hibernate.dialect.PostgreSQLDialect
        legacy_limit_handler: true
        default_schema: public

        # Xử lý hàng loạt (từ application-optimized-db.yaml)
        order_inserts: true
        order_updates: true
        default_batch_fetch_size: 200

        # Tối ưu hóa truy vấn (từ application-optimized-db.yaml)
        query:
          in_clause_parameter_padding: true
          fail_on_pagination_over_collection_fetch: true
          plan_cache_max_size: 4096
          plan_parameter_metadata_max_size: 256

        # Quản lý kết nối - tối ưu cho độ ổn định (từ application-optimized-db.yaml)
        connection:
          provider_disables_autocommit: false
          handling_mode: DELAYED_ACQUISITION_AND_RELEASE_AFTER_TRANSACTION
          autoReconnect: true
          autoReconnectForPools: true
          is-connection-validation-required: true
          # Aggressive timeout checking
          checkout_timeout: 10000
          # Retry failed connections
          retry_attempts: 3
          retry_delay: 1000

        # Cấu hình hiệu suất (từ application-optimized-db.yaml)
        generate_statistics: false

        # Giám sát phiên
        session:
          events:
            log:
              LOG_QUERIES_SLOWER_THAN_MS: 1000

        # Bộ nhớ đệm cấp hai (từ application-no-cache.yaml)
        cache:
          use_second_level_cache: false
          use_query_cache: false
          region:
            factory_class:

        # Chiến lược đặt tên vật lý
        physical_naming_strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl

        # Tối ưu hóa lấy dữ liệu (từ application-optimized-db.yaml)
        max_fetch_depth: 3

        # Gộp câu lệnh
        jdbc.batch_versioned_data: true

  main:
    allow-bean-definition-overriding: true

  # Cấu hình Redis
  data:
    redis:
      host: ${REDIS_HOST:************}
      port: ${REDIS_PORT:30679}
#      password: 2m0881Xc30Wh
      database: ${REDIS_DATABASE:0}
      timeout: ${REDIS_TIMEOUT:5000}
      cache:
        enabled: false # từ application-no-cache.yaml
    mongodb:
      uri: ${SPRING_MONGODB_URI:***************************************************
      database: future

  # Bộ nhớ đệm Freemarker (từ application-no-cache.yaml)
  freemarker:
    cache: false

  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP:************:30092}
    properties:
      security.protocol: PLAINTEXT
    listener:
      type: single
      concurrency: 9
      ack-mode: manual
    producer:
      retries: ${KAFKA_PRODUCER_RETRIES:3}
      batch-size: ${KAFKA_PRODUCER_BATCH_SIZE:256}
      linger: ${KAFKA_PRODUCER_LINGER:1}
      buffer-memory: ${KAFKA_PRODUCER_BUFFER_MEMORY:1048576}
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      group-id: ${KAFKA_CONSUMER_GROUP_ID:future-group}
      enable-auto-commit: false  # Disable auto-commit để sử dụng manual ack
      session-timeout: ${KAFKA_CONSUMER_SESSION_TIMEOUT:15000}
      auto-commit-interval: ${KAFKA_CONSUMER_AUTO_COMMIT_INTERVAL:1000}
      auto-offset-reset: ${KAFKA_CONSUMER_AUTO_OFFSET_RESET:earliest}
      max-poll-records: 50
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    topic:
      replication-factor: ${KAFKA_REPLICATION_FACTOR:1}
      partitions: ${KAFKA_PARTITIONS:1}

  security:
    oauth2:
      resource-server:
        jwt:
          issuer-uri: http://************:32082/realms/cex-lotus
      client:
        registration:
          keycloak:
            client-id: internal-service
            client-secret: X9EopoZ44E0gmdBrVpia8zZI9xDsR37e
            authorization-grant-type: client_credentials
            scope: openid
        provider:
          keycloak:
            issuer-uri: http://************:32082/realms/cex-lotus
            token-uri: http://************:32082/realms/cex-lotus/protocol/openid-connect/token

# Snapshot management configuration
snapshot:
  # Change-based snapshot triggers
  change:
    threshold: 100                    # Save snapshot after N order changes
  time:
    threshold:
      minutes: 5                      # Save snapshot after N minutes regardless
  enabled: true                       # Enable/disable change-based snapshots

  # Cleanup configuration
  cleanup:
    enabled: true                     # Enable/disable automatic cleanup
    retention:
      days: 7                         # Keep snapshots for N days
    max:
      per:
        symbol: 100                   # Maximum snapshots per symbol
    batch:
      size: 50                        # Delete N snapshots per batch

# Chủ đề Kafka
topic-kafka:
  contract:
    order-new: contract-order-new
    order-cancel: contract-order-cancel
    order-completed: contract-order-completed
    trade: contract-trade
    trade-plate: contract-trade-plate
    position: contract-position
    mark-price: contract-mark-price
    index-price: contract-index-price
    funding-rate: contract-funding-rate
    liquidation: contract-liquidation
    order-commands: order-commands
    order-events: order-events
    order-routing: order-routing  # NEW - Topic cho intelligent sharding
    last-price: contract-last-price
  minus:
    wallet-spot: minus-balance-wallet-spot

# Cấu hình định giá hợp đồng
contract:
  pricing:
    cache-update-interval: 5000
    slow-cache-update-interval: 5000
    realtime-update-enabled: true
    realtime-update-interval: 5000
    price-fluctuation-range: 0.05
    price-change-probability: 30
    cache-update-enabled: true
    index-price-update-interval: 1000
    mark-price-update-interval: 1000
    funding-rate-update-interval: 60000
    market-data-update-cron: 0 0 * * * *
  kline:
    realtime-update-enabled: true
    realtime-update-interval: 20
    # Danh sách các khung thời gian được cập nhật theo thời gian thực
    realtime-update-periods: 1min,5min,15min,30min,60min,4hour,1day,1week,1mon
  special-order:
    trigger-process-interval: 1000
    time-process-interval: 5000
  liquidation:
    check-interval: 5000
    max-positions-per-batch: 100
    enabled: false  # TẮT THANH LÝ TỰ ĐỘNG
  last-price:
    # Cấu hình đồng bộ giá cuối cùng giữa các instance
    sync-enabled: true
    # Thời gian cache Redis (giờ)
    redis-cache-ttl: 24
    # Khoảng thời gian lưu định kỳ vào database (phút)
    periodic-save-interval: 5
    # Bật/tắt tự động khôi phục giá cuối cùng khi khởi động
    auto-recovery-enabled: true
    # Timeout cho Redis operations (ms)
    redis-timeout: 3000

# Cấu hình nhóm luồng
thread-pool:
  core-size: 10
  max-size: 20
  queue-capacity: 100
  keep-alive-seconds: 60

# Cấu hình bộ ngắt mạch
circuit-breaker:
  enabled: true
  failure-threshold: 5
  reset-timeout-seconds: 60

# Cấu hình bộ tạo ID Snowflake
snowflake:
  worker-id: ${SNOWFLAKE_WORKER_ID:-1}

# Cấu hình phân tích truy vấn
query:
  analyzer:
    enabled: true
    n-plus-one-threshold: 10
    slow-query-threshold: 1000
  limiter:
    enabled: true
    default-limit: 1000
    max-limit: 10000

# Cấu hình WebClient
webclient:
  timeout:
    connect: 5000
    read: 5000
    write: 5000
    response: 10000
  max-in-memory-size: 10485760
  max-connections: 500
  max-idle-time: 30
  acquire-timeout: 45

# Cấu hình thử lại
retry:
  max-attempts: 3
  wait-duration: 1000
  max-wait-duration: 5000
  wait-duration-multiplier: 1.5

# Cấu hình ghi log
logging:
  level:
    root: INFO
    com.icetea.lotus: INFO
    org.springframework.kafka: INFO
    org.apache.kafka: INFO
    org.hibernate: INFO
    org.springframework.data.redis: INFO # từ application-no-cache.yaml
    org.springframework.cache: INFO # từ application-no-cache.yaml
    com.icetea.lotus.infrastructure.cache.RedisDataService: INFO # từ application-no-cache.yaml
    # Optimization components logging
    com.icetea.lotus.infrastructure.pool: INFO
    com.icetea.lotus.infrastructure.batch: INFO
    com.icetea.lotus.infrastructure.monitoring: INFO
    com.icetea.lotus.infrastructure.matching.SegmentedMatchingEngine: INFO
  file:
    name: logs/contract-perpetual-futures-core.log
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    level: "%5p [${spring.application.name:},%X{traceId:-},%X{spanId:-}]"

# Cấu hình Actuator
management:
  tracing:
    sampling:
      probability: 1.0
  endpoints:
    web:
      exposure:
        include: health,info,prometheus,metrics
  metrics:
    distribution:
      percentiles-histogram:
        http:
          server:
            request: true
    tags:
      application: ${spring.application.name}
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
    livenessState:
      enabled: true
    readinessState:
      enabled: true
  prometheus:
    metrics:
      export:
        enabled: true

# Cấu hình Resilience4j Circuit Breaker
resilience4j:
  circuitbreaker:
    instances:
      orderService:
        registerHealthIndicator: true
        slidingWindowSize: 100
        minimumNumberOfCalls: 10
        permittedNumberOfCallsInHalfOpenState: 5
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: 5s
        failureRateThreshold: 50
        eventConsumerBufferSize: 10
  ratelimiter:
    instances:
      orderService:
        registerHealthIndicator: true
        limitForPeriod: 100
        limitRefreshPeriod: 1s
        timeoutDuration: 0s
        eventConsumerBufferSize: 10
  retry:
    instances:
      orderService:
        maxAttempts: 3
        waitDuration: 1s
        enableExponentialBackoff: true
        exponentialBackoffMultiplier: 2
        retryExceptions: org.springframework.web.client.HttpServerErrorException
        ignoreExceptions: org.springframework.web.client.HttpClientErrorException
        eventConsumerBufferSize: 10

# Cấu hình dịch vụ thị trường
market:
  service:
    name: market
  websocket:
    path: /market/market-ws

# Cấu hình thị trường giao ngay
spot:
  market:
    tracked-symbols: BTC/USDT

# Cấu hình bảo mật CEX
cex-security:
  resource-server-enabled: true  # Enable lại để test fix
  default-principal-name: "internal-service"
  permit-all-endpoints:
    - "/contract-ws/**"
    - "/future/contract-ws/**"
    - "/api/v1/prices/history-kline/**"
    - "/api/v1/prices/mark/**"
    - "/api/v1/prices/last-price/**"

# Cấu hình Keycloak
keycloak:
  auth-server-url: http://************:32082
  realm: cex-lotus
  resource: cex-future
  credentials:
    secret: T63reTvMuOAr1JDVO7Jn1yRvGr0aKhOl

HOSTNAME: 1

# ===================================================================
# FUTURE-CORE OPTIMIZATION CONFIGURATION
# Performance optimization settings for high-frequency trading
# ===================================================================

# Object Pool Configuration
object:
  pool:
    enabled: true
    order:
      initial-size: 10000
      max-size: 50000
      pre-populate: true
    trade:
      initial-size: 10000
      max-size: 50000
      pre-populate: true
    list:
      initial-size: 5000
      max-size: 25000
      pre-populate: true

# Batch Processing Configuration
batch:
  processor:
    enabled: true
    batch-size: 100
    queue-size: 10000
    processing-interval: 1  # milliseconds
    overflow-strategy: DIRECT_PROCESSING  # DIRECT_PROCESSING, DROP, BLOCK
    symbol-grouping: true
    metrics-enabled: true

# Segmented Matching Engine Configuration
segmented:
  matching:
    engine:
      enabled: true
      segment-count: 16
      fair-locking: true
      load-balancing: HASH_BASED  # HASH_BASED, ROUND_ROBIN, LEAST_LOADED
      metrics-enabled: true

# Memory Optimization Configuration
memory:
  optimization:
    enabled: true
    use-order-queue: true  # Use custom OrderQueue instead of CopyOnWriteArrayList
    gc-optimization: true
    memory-monitoring: true

# Performance Monitoring Configuration
performance:
  monitoring:
    enabled: true
    metrics-interval: 1000  # milliseconds
    symbol-metrics-interval: 5000  # milliseconds
    detailed-logging: false
    export-to-micrometer: true

    # TPS Targets
    targets:
      order-tps: 1200000  # 1.2M orders per second
      trade-tps: 600000   # 600K trades per second
      latency-p95: 0.1    # 0.1ms P95 latency

    # Alerting thresholds
    alerts:
      low-tps-threshold: 500000  # Alert if TPS < 500K
      high-latency-threshold: 1.0  # Alert if latency > 1ms
      high-error-rate-threshold: 0.01  # Alert if error rate > 1%
      queue-full-threshold: 0.8  # Alert if queue > 80% full

# Data Structure Optimization
data:
  structure:
    use-concurrent-skip-list-map: true
    use-timestamped-keys: true
    early-exit-optimization: true
    fifo-ordering: true

# Concurrency Configuration
concurrency:
  optimization:
    enabled: true
    reduce-lock-contention: true
    use-local-locks: true  # Instead of distributed locks
    lock-timeout: 10000  # milliseconds
    fair-locks: true

# Cache Configuration
cache:
  optimization:
    enabled: true
    symbol-segment-mapping: true
    order-lookup-cache: true
    price-level-cache: true
    cache-size: 100000

# JVM Optimization Hints
jvm:
  optimization:
    gc-tuning: true
    heap-size: "4g"
    young-gen-size: "1g"
    gc-algorithm: "G1GC"
    gc-threads: 8

    # JVM flags suggestions
    flags:
      - "-XX:+UseG1GC"
      - "-XX:MaxGCPauseMillis=10"
      - "-XX:G1HeapRegionSize=16m"
      - "-XX:+UnlockExperimentalVMOptions"
      - "-XX:+UseStringDeduplication"
      - "-XX:+OptimizeStringConcat"

# Circuit Breaker Configuration
circuit:
  breaker:
    enabled: true
    failure-threshold: 50
    timeout: 30000  # milliseconds
    reset-timeout: 60000  # milliseconds

# Health Check Configuration
health:
  check:
    enabled: true
    interval: 5000  # milliseconds
    thresholds:
      max-queue-size: 8000
      max-latency: 5.0  # milliseconds
      min-tps: 100000
      max-error-rate: 0.05  # 5%

# Feature Flags
features:
  experimental:
    advanced-matching-algorithms: true
    predictive-caching: false
    machine-learning-optimization: false
    adaptive-batching: true
    dynamic-segmentation: false

# Production Optimization
production:
  mode: true
  aggressive-optimization: true
  strict-monitoring: true
  auto-tuning: false  # Manual tuning preferred initially

# ===================================================================
# INTELLIGENT SHARDING CONFIGURATION
# Advanced sharding system for high-performance trading
# ===================================================================

# Intelligent Sharding Configuration
sharding:
  intelligent:
    enabled: true

    # Partition Configuration
    partition:
      max-partitions: 8
      min-partitions: 1
      rebalance-interval: 60000  # 1 minute
      auto-rebalance-enabled: true

    # Load Balancing Configuration
    load:
      overload-threshold: 0.8    # 80%
      underload-threshold: 0.3   # 30%
      monitoring-interval: 10000 # 10 seconds
      history-retention: 3600000 # 1 hour

    # Routing Configuration
    routing:
      large-order-threshold: 10000
      market-order-sequential: true
      routing-timeout: 30000     # 30 seconds
      max-retry-attempts: 3
      price-volatility-threshold: 0.05  # 5%
      market-order-ratio-threshold: 0.3 # 30%

    # Monitoring Configuration
    monitoring:
      metrics-interval: 60000    # 1 minute
      health-check-interval: 30000 # 30 seconds
      statistics-retention: ******** # 24 hours
      detailed-logging: false
      performance-metrics: true

# Pod Configuration
pod:
  name: ${POD_NAME:default-pod}