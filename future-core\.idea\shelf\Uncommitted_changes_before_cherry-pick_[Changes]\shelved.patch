Index: src/main/java/com/icetea/lotus/infrastructure/messaging/consumer/OrderCommandConsumer.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.icetea.lotus.infrastructure.messaging.consumer;\r\n\r\nimport com.fasterxml.jackson.core.JsonProcessingException;\r\nimport com.fasterxml.jackson.databind.ObjectMapper;\r\nimport com.icetea.lotus.core.domain.entity.Order;\r\nimport com.icetea.lotus.core.domain.entity.Trade;\r\nimport com.icetea.lotus.core.domain.service.OrderMatchingEngineService;\r\nimport com.icetea.lotus.infrastructure.messaging.command.OrderCommand;\r\nimport com.icetea.lotus.infrastructure.messaging.producer.OrderEventProducer;\r\nimport com.icetea.lotus.infrastructure.sharding.SymbolShardingManager;\r\nimport lombok.RequiredArgsConstructor;\r\nimport lombok.SneakyThrows;\r\nimport lombok.extern.slf4j.Slf4j;\r\nimport org.springframework.beans.factory.annotation.Value;\r\nimport org.springframework.kafka.annotation.KafkaListener;\r\nimport org.springframework.kafka.core.KafkaTemplate;\r\nimport org.springframework.stereotype.Component;\r\n\r\nimport java.util.List;\r\n\r\n/**\r\n * Consumer cho OrderCommand\r\n */\r\n@Slf4j\r\n@Component\r\n@RequiredArgsConstructor\r\npublic class OrderCommandConsumer {\r\n\r\n    private final SymbolShardingManager shardingManager;\r\n    private final OrderMatchingEngineService orderMatchingEngineService;\r\n    private final OrderEventProducer orderEventProducer;\r\n    private final ObjectMapper objectMapper;\r\n    @Value(\"${topic-kafka.contract.order-commands}\")\r\n    private String orderCommandsTopic;\r\n\r\n    private final KafkaTemplate<String, String> kafkaTemplate;\r\n    /**\r\n     * Xử lý OrderCommand\r\n     */\r\n    @KafkaListener(\r\n        topics = \"${topic-kafka.contract.order-commands}\",\r\n        containerFactory = \"kafkaListenerContainerFactory\",\r\n        groupId = \"contract-perpetual-futures-core-command\"\r\n    )\r\n    @SneakyThrows(JsonProcessingException.class)\r\n    public void handleOrderCommand(String message){\r\n        log.info(\"handleOrderCommand Bắt đầu xử lý command từ topic {}, data = {}\", orderCommandsTopic, message);\r\n        OrderCommand command = objectMapper.readValue(message, OrderCommand.class);\r\n        log.info(\"handleOrderCommand after parse data = {}\", command.toString());\r\n        if (command == null || command.getOrder() == null || command.getOrder().getSymbol() == null) {\r\n            log.error(\"Nhận command không hợp lệ từ topic {}, command = {}\", orderCommandsTopic, command);\r\n            return;\r\n        }\r\n\r\n        log.info(\"Nhận command từ topic {}, type = {}, symbol = {}, orderId = {}\",\r\n                orderCommandsTopic, command.getType(), command.getOrder().getSymbol().getValue(), command.getOrder().getOrderId());\r\n\r\n        String symbol = command.getOrder().getSymbol().getValue();\r\n        log.info(\"handleOrderCommand Symbol: {}\", symbol);\r\n\r\n        // Chỉ xử lý command nếu symbol được gán cho instance này\r\n        if (!shardingManager.isSymbolOwnedByThisPod(symbol)) {\r\n            log.debug(\"Bỏ qua command: {}, symbol: {} không được gán cho pod này\", command.getType(), symbol);\r\n            return;\r\n        }\r\n\r\n        // Xử lý command theo loại\r\n        log.info(\"handleOrderCommand Phân loại message theo type {}\", command.getType());\r\n        switch (command.getType()) {\r\n            case PLACE_ORDER:\r\n                log.debug(\"Xử lý command PLACE_ORDER, symbol: {}\", symbol);\r\n                handlePlaceOrderCommand(command.getOrder());\r\n                break;\r\n\r\n            case CANCEL_ORDER:\r\n                log.debug(\"Xử lý command CANCEL_ORDER, symbol: {}\", symbol);\r\n                handleCancelOrderCommand(command.getOrder());\r\n                break;\r\n\r\n            case UPDATE_ORDER:\r\n                log.debug(\"Xử lý command UPDATE_ORDER, symbol: {}\", symbol);\r\n                handleUpdateOrderCommand(command.getOrder());\r\n                break;\r\n\r\n            default:\r\n                log.warn(\"Không hỗ trợ loại command: {}\", command.getType());\r\n                break;\r\n        }\r\n        log.info(\"END handleOrderCommand, symbol: {}, command = {}\", symbol, command.getCommandId());\r\n    }\r\n\r\n    @KafkaListener(topics = \"action_push\", groupId = \"test-consumer-group\", containerFactory = \"kafkaListenerContainerFactory\")\r\n    public void pushMessage(String message) throws JsonProcessingException {\r\n        log.info(\"Test message received: {}\", message.toString());\r\n        if(message != null){\r\n            OrderCommand orderCommand = new OrderCommand();\r\n            orderCommand.setCommandId(\"123456\");\r\n            kafkaTemplate.send(\"test-consumer-local\", objectMapper.writeValueAsString(orderCommand));\r\n        }\r\n    }\r\n\r\n\r\n    @KafkaListener(topics = \"test-consumer-local\", groupId = \"test-consumer-group\", containerFactory = \"kafkaListenerContainerFactory\")\r\n    public void testMessage(String message) throws JsonProcessingException {\r\n        log.info(\"Test message received: {}\", message.toString());\r\n        OrderCommand orderCommand = objectMapper.readValue(message, OrderCommand.class);\r\n        log.info(\"Test message received parse: {}\",orderCommand.toString());\r\n    }\r\n\r\n    /**\r\n     * Xử lý command đặt lệnh\r\n     *\r\n     * @param order Lệnh\r\n     */\r\n    private void handlePlaceOrderCommand(Order order) {\r\n        try {\r\n            // Xử lý lệnh bằng OrderMatchingEngineService\r\n            List<Trade> trades = orderMatchingEngineService.processOrder(order);\r\n            log.info(\"handlePlaceOrderCommand: {}\", trades.toString());\r\n            // Gửi event\r\n            orderEventProducer.publishOrderPlacedEvent(order, trades);\r\n        } catch (Exception e) {\r\n            log.error(\"Lỗi khi xử lý command đặt lệnh: {}\", order.getOrderId(), e);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Xử lý command hủy lệnh\r\n     *\r\n     * @param order Lệnh\r\n     */\r\n    private void handleCancelOrderCommand(Order order) {\r\n        try {\r\n            // Hủy lệnh bằng OrderMatchingEngineService\r\n            boolean success = orderMatchingEngineService.cancelOrder(order);\r\n\r\n            if (success) {\r\n                // Gửi event\r\n                orderEventProducer.publishOrderCancelledEvent(order);\r\n            }\r\n        } catch (Exception e) {\r\n            log.error(\"Lỗi khi xử lý command hủy lệnh: {}\", order.getOrderId(), e);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Xử lý command cập nhật lệnh\r\n     *\r\n     * @param order Lệnh\r\n     */\r\n    private void handleUpdateOrderCommand(Order order) {\r\n        try {\r\n            // Hiện tại OrderMatchingEngineService không có phương thức updateOrder\r\n            // Nên chúng ta sẽ hủy lệnh cũ và đặt lệnh mới\r\n            boolean cancelSuccess = orderMatchingEngineService.cancelOrder(order);\r\n\r\n            if (cancelSuccess) {\r\n                // Đặt lệnh mới\r\n                orderMatchingEngineService.processOrder(order);\r\n\r\n                // Gửi event\r\n                orderEventProducer.publishOrderUpdatedEvent(order);\r\n            }\r\n        } catch (Exception e) {\r\n            log.error(\"Lỗi khi xử lý command cập nhật lệnh: {}\", order.getOrderId(), e);\r\n        }\r\n    }\r\n\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/java/com/icetea/lotus/infrastructure/messaging/consumer/OrderCommandConsumer.java b/src/main/java/com/icetea/lotus/infrastructure/messaging/consumer/OrderCommandConsumer.java
--- a/src/main/java/com/icetea/lotus/infrastructure/messaging/consumer/OrderCommandConsumer.java	(revision 070f8cf1f8a44074fbfc00806e8fd4af5da72628)
+++ b/src/main/java/com/icetea/lotus/infrastructure/messaging/consumer/OrderCommandConsumer.java	(date 1752079639893)
@@ -40,7 +40,7 @@
     @KafkaListener(
         topics = "${topic-kafka.contract.order-commands}",
         containerFactory = "kafkaListenerContainerFactory",
-        groupId = "contract-perpetual-futures-core-command"
+        groupId = "${spring.kafka.consumer.group-id}"
     )
     @SneakyThrows(JsonProcessingException.class)
     public void handleOrderCommand(String message){
