package com.icetea.lotus.job;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.client.CexApiClientFactory;
import com.icetea.lotus.component.CoinExchangeRate;
import com.icetea.lotus.entity.ExchangeCoin;
import com.icetea.lotus.handler.MongoMarketHandler;
import com.icetea.lotus.handler.WebsocketMarketHandler;
import com.icetea.lotus.processor.CoinProcessor;
import com.icetea.lotus.processor.CoinProcessorFactory;
import com.icetea.lotus.processor.DefaultCoinProcessor;
import com.icetea.lotus.service.ExchangeCoinService;
import com.icetea.lotus.service.MarketService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

/**
 * Automatically synchronize trading pairs in Exchange match trading center
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class CoinProcessorJob {

    private final CoinProcessorFactory processorFactory;
    private final ExchangeCoinService coinService;
    private final RestTemplate restTemplate;
    private final MongoMarketHandler mongoMarketHandler;
    private final WebsocketMarketHandler wsHandler;
    private final MarketService marketService;
    private final CoinExchangeRate exchangeRate;
    private final CexApiClientFactory cexApiClientFactory;
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Value("${cex-services.exchange-service}")
    private String exchangeServiceName;

    /**
     * 1-minute timer, every 1 minute
     */
    @Scheduled(cron = "0 */1 * * * ?")
    public void synchronizeExchangeCenter() {
        log.info("========CoinProcessorJob========> synchronize the exchange coin pairs");

        Map<String, Integer> exchangeCenterCoins = null;

        try {
            // Get the currency supported by the Matching Trading Center
            String apiUri = "/exchange/monitor/engines";

            ResponseEntity<Map<String, Integer>> response = cexApiClientFactory.getClient(exchangeServiceName).get(
                    apiUri,
                    new ParameterizedTypeReference<>() {
                    }
            );

            exchangeCenterCoins = response.getBody();
            log.info("Parsed response: {}", exchangeCenterCoins);
        } catch (Exception e) {
            log.error("Failed to get exchange center coins from {}, using fallback strategy: {}", exchangeServiceName, e.getMessage());

            // Fallback: Enable K-line generation for all existing processors
            enableKLineForAllProcessors();
            return;
        }

        if (exchangeCenterCoins == null || exchangeCenterCoins.isEmpty()) {
            log.warn("Exchange center coins is null or empty, using fallback strategy");
            enableKLineForAllProcessors();
            return;
        }

        try {
            log.info("========CoinProcessorJob========> now exchange support coins:{}", objectMapper.writeValueAsString(exchangeCenterCoins));
        } catch (JsonProcessingException e) {
            log.error("Error serializing exchangeCenterCoins", e);
        }
        Map<String, CoinProcessor> processorMap = processorFactory.getProcessorMap();

        log.info("========CoinProcessorJob========> now market support coins");

        // Determine whether the currency that exists in the matching trading center exists in the market
        for (Map.Entry<String, Integer> coin : exchangeCenterCoins.entrySet()) {
            String symbol = coin.getKey();
            Integer status = coin.getValue();
            // Is there a processor for this currency?
            if (processorMap.containsKey(symbol)) {
                CoinProcessor temProcessor = processorMap.get(symbol);
                if (status == 1) {
                    // The matching transaction is enabled, then the K-line should be processed in the market.
                    if (temProcessor.isStopKline()) {
                        temProcessor.setIsStopKLine(false);
                        log.info("[Start] {} will start generate KLine.", symbol);
                    }
                } else if (status == 2) {
                    // Stop state, then the K-line processing should be stopped in the market
                    if (!temProcessor.isStopKline()) {
                        log.info("[Stop]{} will stop generate KLine.", symbol);
                        temProcessor.setIsStopKLine(true);
                    }
                }
                continue;
            }

            // Is this currency present in the database?
            ExchangeCoin focusCoin = coinService.findBySymbol(symbol);
            if (focusCoin == null) {
                continue;
            }

            log.info("============[Start]initialized New CoinProcessor({}) start=====================", symbol);
            // Create a new Processor
            CoinProcessor processor = new DefaultCoinProcessor(symbol, focusCoin.getBaseSymbol());
            processor.addHandler(mongoMarketHandler);
            processor.addHandler(wsHandler);
            processor.setMarketService(marketService);
            processor.setExchangeRate(exchangeRate);
            processor.initializeThumb();
            processor.initializeUsdRate();
            processor.setIsHalt(false);

            if (status == 2) {
                processor.setIsStopKLine(true);
            }
            processorFactory.addProcessor(symbol, processor);

            log.info("============[End]initialized  New CoinProcessor({}) end=====================", symbol);
        }
    }

    /**
     * Fallback method to enable K-line generation for all existing processors
     * when exchange service is not available
     */
    private void enableKLineForAllProcessors() {
        log.info("========CoinProcessorJob========> Enabling K-line generation for all processors (fallback)");

        Map<String, CoinProcessor> processorMap = processorFactory.getProcessorMap();

        for (Map.Entry<String, CoinProcessor> entry : processorMap.entrySet()) {
            String symbol = entry.getKey();
            CoinProcessor processor = entry.getValue();

            if (processor.isStopKline()) {
                processor.setIsStopKLine(false);
                log.info("[Fallback] {} K-line generation enabled", symbol);
            }
        }

        log.info("========CoinProcessorJob========> Fallback completed, enabled K-line for {} processors", processorMap.size());
    }

    /**
     * Debug method to check K-line generation status for all processors
     * Runs every 5 minutes
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void debugKLineStatus() {
        Map<String, CoinProcessor> processorMap = processorFactory.getProcessorMap();

        log.info("========CoinProcessorJob========> K-line status check for {} processors", processorMap.size());

        int enabledCount = 0;
        int disabledCount = 0;

        for (Map.Entry<String, CoinProcessor> entry : processorMap.entrySet()) {
            String symbol = entry.getKey();
            CoinProcessor processor = entry.getValue();

            if (processor.isStopKline()) {
                log.debug("K-line DISABLED for symbol: {}", symbol);
                disabledCount++;
            } else {
                log.debug("K-line ENABLED for symbol: {}", symbol);
                enabledCount++;
            }
        }

        log.info("========CoinProcessorJob========> K-line status summary: {} enabled, {} disabled", enabledCount, disabledCount);

        if (disabledCount > 0) {
            log.warn("Some processors have K-line generation disabled. Check exchange service connectivity.");
        }
    }
}
