package com.icetea.lotus.processor;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.icetea.lotus.component.CoinExchangeRate;
import com.icetea.lotus.entity.CoinThumb;
import com.icetea.lotus.entity.ExchangeTrade;
import com.icetea.lotus.entity.KLine;
import com.icetea.lotus.handler.MarketHandler;
import com.icetea.lotus.service.MarketService;
import lombok.Getter;
import lombok.ToString;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Default transaction processor generates 1m K-line information
 */
@ToString
public class DefaultCoinProcessor implements CoinProcessor {
    private static final Logger logger = LoggerFactory.getLogger(DefaultCoinProcessor.class);
    private static final int SCALE = 4;
    private static final String PERIOD_1MIN = "1min";
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Getter
    private final String symbol;
    private final String baseCoin;
    private final List<MarketHandler> handlers;
    private final ReentrantLock thumbLock = new ReentrantLock();
    private final ReentrantLock kLineLock = new ReentrantLock();

    private KLine currentKLine;
    private CoinThumb coinThumb;
    private MarketService service;
    private CoinExchangeRate coinExchangeRate;
    // Whether to deal with it temporarily
    private volatile boolean isHalt = true;
    // Whether to stop K-line generation
    private volatile boolean stopKLine = false;

    public  DefaultCoinProcessor(String symbol, String baseCoin) {
        this.handlers = new ArrayList<>();
        this.baseCoin = baseCoin;
        this.symbol = symbol;
        createNewKLine();
        this.coinThumb = new CoinThumb();
        this.coinThumb.setSymbol(symbol);
    }

    @Override
    public void initializeThumb() {
        Calendar calendar = Calendar.getInstance();
        // Set the seconds and microsecond fields to 0
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        long nowTime = calendar.getTimeInMillis();
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        long firstTimeOfToday = calendar.getTimeInMillis();

        logger.info("initializeThumb from {} to {}", firstTimeOfToday, nowTime);
        List<KLine> lines = service.findAllKLine(this.symbol, firstTimeOfToday, nowTime, PERIOD_1MIN);

        try {
            thumbLock.lock();
            // Reset thumb values
            coinThumb.setOpen(BigDecimal.ZERO);
            coinThumb.setHigh(BigDecimal.ZERO);
            coinThumb.setLow(BigDecimal.ZERO);
            coinThumb.setClose(BigDecimal.ZERO);
            coinThumb.setVolume(BigDecimal.ZERO);
            coinThumb.setTurnover(BigDecimal.ZERO);

            // Process all K-lines to build the thumb
            for (KLine kline : lines) {
                if (kline.getOpenPrice().compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }

                // Set open price if not set yet
                if (coinThumb.getOpen().compareTo(BigDecimal.ZERO) == 0) {
                    coinThumb.setOpen(kline.getOpenPrice());
                }

                // Update high price if the current is higher
                if (coinThumb.getHigh().compareTo(kline.getHighestPrice()) < 0) {
                    coinThumb.setHigh(kline.getHighestPrice());
                }

                // Update low price if current is lower and valid
                if (kline.getLowestPrice().compareTo(BigDecimal.ZERO) > 0 &&
                        (coinThumb.getLow().compareTo(BigDecimal.ZERO) == 0 ||
                                coinThumb.getLow().compareTo(kline.getLowestPrice()) > 0)) {
                    coinThumb.setLow(kline.getLowestPrice());
                }

                // Update close price if valid
                if (kline.getClosePrice().compareTo(BigDecimal.ZERO) > 0) {
                    coinThumb.setClose(kline.getClosePrice());
                }

                // Accumulate volume and turnover
                coinThumb.setVolume(coinThumb.getVolume().add(kline.getVolume()));
                coinThumb.setTurnover(coinThumb.getTurnover().add(kline.getTurnover()));
            }

            // Calculate change and percentage
            BigDecimal change = coinThumb.getClose().subtract(coinThumb.getOpen());
            coinThumb.setChange(change);

            // Calculate percentage change if open price is valid
            if (coinThumb.getOpen().compareTo(BigDecimal.ZERO) > 0) {
                coinThumb.setChg(change.divide(coinThumb.getOpen(), SCALE, RoundingMode.UP));
            }
        } finally {
            thumbLock.unlock();
        }
    }

    /**
     * Creates a new KLine for the next minute
     */
    public void createNewKLine() {
        try {
            kLineLock.lock();
            currentKLine = new KLine();

            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            // Set time to the next full minute
            calendar.add(Calendar.MINUTE, 1);

            currentKLine.setTime(calendar.getTimeInMillis());
            currentKLine.setPeriod(PERIOD_1MIN);
            currentKLine.setCount(0);
        } finally {
            kLineLock.unlock();
        }
    }

    /**
     * Reset CoinThumb at 00:00:00 for the new day
     */
    @Override
    public void resetThumb() {
        logger.info("Resetting coin thumb for {}", symbol);
        try {
            thumbLock.lock();
            // Save the last day's closing price
            BigDecimal lastClose = coinThumb.getClose();

            // Reset values for the new day
            coinThumb.setOpen(BigDecimal.ZERO);
            coinThumb.setHigh(BigDecimal.ZERO);
            coinThumb.setLow(BigDecimal.ZERO);
            coinThumb.setChg(BigDecimal.ZERO);
            coinThumb.setChange(BigDecimal.ZERO);

            // Set yesterday's closing price
            coinThumb.setLastDayClose(lastClose);
        } finally {
            thumbLock.unlock();
        }
    }

    @Override
    public void setExchangeRate(CoinExchangeRate coinExchangeRate) {
        this.coinExchangeRate = coinExchangeRate;
    }

    /**
     * Updates the 24-hour trading volume
     *
     * @param time Current time in milliseconds
     */
    @Override
    public void update24HVolume(long time) {
        if (coinThumb == null) {
            logger.warn("Cannot update 24H volume: coinThumb is null for {}", symbol);
            return;
        }

        try {
            thumbLock.lock();
            // Calculate 24 hours ago
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(time);
            calendar.add(Calendar.HOUR_OF_DAY, -24);
            long timeStart = calendar.getTimeInMillis();

            // Get and set the 24-hour volume
            BigDecimal volume = service.findTradeVolume(this.symbol, timeStart, time);
            coinThumb.setVolume(volume.setScale(SCALE, RoundingMode.DOWN));

            logger.debug("Updated 24H volume for {}: {}", symbol, volume);
        } finally {
            thumbLock.unlock();
        }
    }

    /**
     * Initializes USD exchange rate for the coin
     */
    @Override
    public void initializeUsdRate() {
        if (coinExchangeRate == null) {
            logger.warn("Cannot initialize USD rate: coinExchangeRate is null for {}", symbol);
            return;
        }

        try {
            thumbLock.lock();
            // Get base coin USD rate
            BigDecimal baseUsdRate = coinExchangeRate.getUsdRate(baseCoin);
            coinThumb.setBaseUsdRate(baseUsdRate);

            // Calculate and set USD rate for this coin
            BigDecimal closePrice = coinThumb.getClose();
            BigDecimal usdRate = closePrice.multiply(baseUsdRate);
            coinThumb.setUsdRate(usdRate);

            logger.debug("Initialized USD rate for {}: base={}, close={}, usdRate={}",
                    symbol, baseUsdRate, closePrice, usdRate);
        } finally {
            thumbLock.unlock();
        }
    }

    /**
     * Generates K-lines for different time periods based on the current time
     *
     * @param time   Current time in milliseconds
     * @param minute Current minute
     * @param hour   Current hour
     */
    @Override
    public void generateKLine(long time, int minute, int hour) {
        if (stopKLine) {
            logger.debug("K-line generation is stopped for {}", symbol);
            return;
        }

        logger.info("Generating K-lines for {}", symbol);
        long startTime = System.currentTimeMillis();

        // Generate a 1-minute K-line
        this.autoGenerate();
        this.generateKLine1min(1, Calendar.MINUTE, time);

        // Update 24H transaction volume
        this.update24HVolume(time);

        // Generate K-lines for different time periods
        if (minute % 5 == 0) {
            this.generateKLine(5, Calendar.MINUTE, time);
        }
        if (minute % 10 == 0) {
            this.generateKLine(10, Calendar.MINUTE, time);
        }
        if (minute % 15 == 0) {
            this.generateKLine(15, Calendar.MINUTE, time);
        }
        if (minute % 30 == 0) {
            this.generateKLine(30, Calendar.MINUTE, time);
        }

        // Reset thumb at midnight
        if (hour == 0 && minute == 0) {
            this.resetThumb();
        }

        long duration = System.currentTimeMillis() - startTime;
        logger.info("Completed K-line generation for {} in {}ms", symbol, duration);
    }

    /**
     * Automatically generates a 1-minute K-line using the current data
     */
    @Override
    public void autoGenerate() {
        if (coinThumb == null) {
            logger.warn("Cannot auto-generate K-line: coinThumb is null for {}", symbol);
            return;
        }

        try {
            kLineLock.lock();

            DateFormat df = new SimpleDateFormat("HH:mm:ss");
            logger.info("Auto-generating 1min K-line at {} for {}",
                    df.format(new Date(currentKLine.getTime())), symbol);

            // Store the previous transaction price when there is no transaction price
            if (currentKLine.getOpenPrice().compareTo(BigDecimal.ZERO) == 0) {
                BigDecimal closePrice = coinThumb.getClose();
                currentKLine.setOpenPrice(closePrice);
                currentKLine.setLowestPrice(closePrice);
                currentKLine.setHighestPrice(closePrice);
                currentKLine.setClosePrice(closePrice);
            }

            // Set the current time
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            currentKLine.setTime(calendar.getTimeInMillis());

            // Create a new K-line for the next minute
            createNewKLine();

            if (logger.isDebugEnabled()) {
                try {
                    logger.debug("Generated K-line data: {}", objectMapper.writeValueAsString(currentKLine));
                } catch (JsonProcessingException e) {
                    logger.error("Error serializing currentKLine", e);
                }
            }
        } finally {
            kLineLock.unlock();
        }
    }

    /**
     * Generates a 1-minute K-line for the specified time range
     *
     * @param range The range value (e.g., 1, 5, 10, etc.)
     * @param field The calendar field (e.g., Calendar.MINUTE, Calendar.HOUR_OF_DAY, etc.)
     * @param time  The end time in milliseconds
     */
    @Override
    public void generateKLine1min(int range, int field, long time) {
        if (stopKLine) {
            logger.debug("K-line generation is stopped for {}", symbol);
            return;
        }

        // Generate the K-line and store it
        KLine kLine = generateKLineData(range, field, time);

        // Ensure the K-line has the latest trade price
        try {
            thumbLock.lock();
            if (coinThumb != null && coinThumb.getClose().compareTo(BigDecimal.ZERO) > 0) {
                // Update the K-line close price with the latest trade price from coinThumb
                kLine.setClosePrice(coinThumb.getClose());
                // Update highest price if needed
                if (coinThumb.getClose().compareTo(kLine.getHighestPrice()) > 0) {
                    kLine.setHighestPrice(coinThumb.getClose());
                }
                // Update lowest price if needed
                if (kLine.getLowestPrice().compareTo(BigDecimal.ZERO) == 0 || 
                    coinThumb.getClose().compareTo(kLine.getLowestPrice()) < 0) {
                    kLine.setLowestPrice(coinThumb.getClose());
                }
                logger.debug("Updated K-line close price with latest trade price: {}", coinThumb.getClose());
            }
        } finally {
            thumbLock.unlock();
        }

        handleKLineStorage(kLine);
    }


    /**
     * Sets whether processing should be halted
     *
     * @param status True to halt processing, false to enable it
     */
    @Override
    public void setIsHalt(boolean status) {
        this.isHalt = status;
    }

    /**
     * Processes a list of trades, updating K-lines and thumbs
     *
     * @param trades The list of trades to process
     */
    @Override
    public void process(List<ExchangeTrade> trades) {
        if (isHalt) {
            logger.debug("Processing is halted for {}", symbol);
            return;
        }

        if (trades == null || trades.isEmpty()) {
            return;
        }

        try {
            kLineLock.lock();
            for (ExchangeTrade exchangeTrade : trades) {
                // Update the current K-line with trade data
                processTrade(currentKLine, exchangeTrade);

                // Process today's overview information
                if (logger.isDebugEnabled()) {
                    logger.debug("Processing overview information for {} with trade: {}",
                            symbol, exchangeTrade.getPrice());
                }

                // Update the thumb with the latest trade
                handleThumb(exchangeTrade);

                // Store and push transaction information
                handleTradeStorage(exchangeTrade);
            }
        } finally {
            kLineLock.unlock();
        }
    }

    /**
     * Updates a K-line with trade data
     *
     * @param kLine         The K-line to update
     * @param exchangeTrade The trade data
     */
    public void processTrade(KLine kLine, ExchangeTrade exchangeTrade) {
        BigDecimal tradePrice = exchangeTrade.getPrice();
        BigDecimal tradeAmount = exchangeTrade.getAmount();

        if (kLine.getClosePrice().compareTo(BigDecimal.ZERO) == 0) {
            // Set the K-line value for the first time
            kLine.setOpenPrice(tradePrice);
            kLine.setHighestPrice(tradePrice);
            kLine.setLowestPrice(tradePrice);
            kLine.setClosePrice(tradePrice);
        } else {
            // Update high, low, and close prices
            kLine.setHighestPrice(tradePrice.max(kLine.getHighestPrice()));
            kLine.setLowestPrice(tradePrice.min(kLine.getLowestPrice()));
            kLine.setClosePrice(tradePrice);
        }

        // Update count, volume, and turnover
        kLine.setCount(kLine.getCount() + 1);
        kLine.setVolume(kLine.getVolume().add(tradeAmount));

        BigDecimal turnover = tradePrice.multiply(tradeAmount);
        kLine.setTurnover(kLine.getTurnover().add(turnover));
    }

    /**
     * Stores and pushes trade information to all registered handlers
     *
     * @param exchangeTrade The trade data to store
     */
    public void handleTradeStorage(ExchangeTrade exchangeTrade) {
        if (handlers == null || handlers.isEmpty()) {
            return;
        }

        for (MarketHandler storage : handlers) {
            if (storage != null) {
                storage.handleTrade(symbol, exchangeTrade, coinThumb);
            }
        }
    }

    /**
     * Stores and pushes K-line information to all registered handlers
     *
     * @param kLine The K-line data to store
     */
    public void handleKLineStorage(KLine kLine) {
        if (handlers == null || handlers.isEmpty()) {
            return;
        }

        for (MarketHandler storage : handlers) {
            if (storage != null) {
                storage.handleKLine(symbol, kLine);
            }
        }
    }

    /**
     * Updates the coin thumb with the latest trade data
     *
     * @param exchangeTrade The trade data
     */
    public void handleThumb(ExchangeTrade exchangeTrade) {
        if (logger.isDebugEnabled()) {
            logger.debug("Updating thumb for {}", symbol);
        }

        try {
            thumbLock.lock();

            BigDecimal tradePrice = exchangeTrade.getPrice();
            BigDecimal tradeAmount = exchangeTrade.getAmount();

            // Set open price if not set yet
            if (coinThumb.getOpen().compareTo(BigDecimal.ZERO) == 0) {
                coinThumb.setOpen(tradePrice);
            }

            // Update high price
            coinThumb.setHigh(tradePrice.max(coinThumb.getHigh()));

            // Update low price
            if (coinThumb.getLow().compareTo(BigDecimal.ZERO) == 0) {
                coinThumb.setLow(tradePrice);
            } else {
                coinThumb.setLow(tradePrice.min(coinThumb.getLow()));
            }

            // Update close price
            coinThumb.setClose(tradePrice);

            // Update volume and turnover
            coinThumb.setVolume(coinThumb.getVolume().add(tradeAmount).setScale(SCALE, RoundingMode.UP));
            BigDecimal turnover = tradePrice.multiply(tradeAmount).setScale(SCALE, RoundingMode.UP);
            coinThumb.setTurnover(coinThumb.getTurnover().add(turnover));

            // Calculate and update change and percentage
            BigDecimal change = coinThumb.getClose().subtract(coinThumb.getOpen());
            coinThumb.setChange(change);

            if (coinThumb.getOpen().compareTo(BigDecimal.ZERO) > 0) {
                coinThumb.setChg(change.divide(coinThumb.getOpen(), SCALE, RoundingMode.UP));
            }

            // Update USD rates
            if (coinExchangeRate != null) {
                BigDecimal baseUsdRate = coinExchangeRate.getUsdRate(baseCoin);
                coinThumb.setBaseUsdRate(baseUsdRate);

                if ("USDT".equalsIgnoreCase(baseCoin)) {
                    coinThumb.setUsdRate(tradePrice);
                } else {
                    coinThumb.setUsdRate(tradePrice.multiply(baseUsdRate));
                }

                if (logger.isDebugEnabled()) {
                    logger.debug("Updated USD rate for {}: {}", symbol, coinThumb.getUsdRate());
                }
            }

            if (logger.isTraceEnabled()) {
                logger.trace("Updated thumb for {}: {}", symbol, coinThumb);
            }
        } finally {
            thumbLock.unlock();
        }
    }

    /**
     * Adds a market handler to process and store market data
     *
     * @param storage The market handler to add
     */
    @Override
    public void addHandler(MarketHandler storage) {
        if (storage != null) {
            handlers.add(storage);
        }
    }

    /**
     * Gets the current coin thumb (market overview)
     *
     * @return The current coin thumb
     */
    @Override
    public CoinThumb getThumb() {
        return coinThumb;
    }

    /**
     * Sets the market service for database operations
     *
     * @param service The market service to use
     */
    @Override
    public void setMarketService(MarketService service) {
        this.service = service;
    }

    /**
     * Generates a K-line for the specified time range and saves it to the database
     *
     * @param range The range value (e.g., 1, 5, 10, etc.)
     * @param field The calendar field (e.g., Calendar.MINUTE, Calendar.HOUR_OF_DAY, etc.)
     * @param time  The end time in milliseconds
     */
    @Override
    public void generateKLine(int range, int field, long time) {
        if (stopKLine) {
            logger.debug("K-line generation is stopped for {}", symbol);
            return;
        }

        KLine kLine = generateKLineData(range, field, time);
        service.saveKLine(symbol, kLine);
    }

    /**
     * Helper method to generate K-line data for a specific time range
     *
     * @param range The range value (e.g., 1, 5, 10, etc.)
     * @param field The calendar field (e.g., Calendar.MINUTE, Calendar.HOUR_OF_DAY, etc.)
     * @param time  The end time in milliseconds
     * @return The generated K-line
     */
    private KLine generateKLineData(int range, int field, long time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(time);
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long endTick = calendar.getTimeInMillis();

        // Push range time units forward
        calendar.add(field, -range);
        long startTick = calendar.getTimeInMillis();

        if (logger.isDebugEnabled()) {
            String endTime = df.format(new Date(endTick));
            String fromTime = df.format(new Date(startTick));
            logger.debug("Time range for {} K-line: {} to {}", symbol, fromTime, endTime);
        }

        // Create and configure the K-line
        KLine kLine = new KLine();
        // Fix: K-line time should be the start time of the period, not end time
        kLine.setTime(startTick);

        // Determine the range unit based on the field
        String rangeUnit = determineRangeUnit(field);
        kLine.setPeriod(range + rangeUnit);

        // Process trades for the K-line
        if (field == Calendar.MINUTE || field == Calendar.HOUR_OF_DAY || field == Calendar.DAY_OF_YEAR) {
            // For minute, hour, and day lines, query trade details directly
            List<ExchangeTrade> exchangeTrades = service.findTradeByTimeRange(this.symbol, startTick, endTick);

            // Process each trade to update the K-line
            for (ExchangeTrade exchangeTrade : exchangeTrades) {
                processTrade(kLine, exchangeTrade);
            }
        } else {
            // For weekly and monthly lines, use a more efficient approach
            processKline(kLine, startTick, endTick, field);
        }

        // If no trades occurred, use the last known price
        if (kLine.getOpenPrice().compareTo(BigDecimal.ZERO) == 0) {
            try {
                thumbLock.lock();
                BigDecimal closePrice = coinThumb.getClose();
                kLine.setOpenPrice(closePrice);
                kLine.setClosePrice(closePrice);
                kLine.setLowestPrice(closePrice);
                kLine.setHighestPrice(closePrice);
            } finally {
                thumbLock.unlock();
            }
        }

        String kLineJson = "";
        if (logger.isDebugEnabled()) {
            try {
                kLineJson = objectMapper.writeValueAsString(kLine);
            } catch (JsonProcessingException e) {
                logger.error("Error serializing kLine", e);
            }
        }
        logger.info("Generated {}{} K-line for {} at {}: {}",
                range, rangeUnit, symbol, df.format(new Date(kLine.getTime())), kLineJson);

        return kLine;
    }

    /**
     * Generates K-line for completed time periods only
     * This method ensures K-lines are generated for past completed periods
     *
     * @param range The range value (e.g., 1, 5, 10, etc.)
     * @param field The calendar field (e.g., Calendar.MINUTE, Calendar.HOUR_OF_DAY, etc.)
     * @param currentTime Current timestamp
     */
    public void generateCompletedKLine(int range, int field, long currentTime) {
        if (stopKLine) {
            logger.debug("K-line generation is stopped for {}", symbol);
            return;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(currentTime);

        // Reset seconds and milliseconds to get the start of current period
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        // For minute-based K-lines, also reset to the start of the minute interval
        if (field == Calendar.MINUTE) {
            int currentMinute = calendar.get(Calendar.MINUTE);
            int intervalStart = (currentMinute / range) * range;
            calendar.set(Calendar.MINUTE, intervalStart);
        }

        // Move to the previous completed period
        calendar.add(field, -range);
        long completedPeriodEnd = calendar.getTimeInMillis();

        // Generate K-line for the completed period
        KLine kLine = generateKLineData(range, field, completedPeriodEnd);
        service.saveKLine(symbol, kLine);

        logger.info("Generated completed {}{} K-line for {} at {}",
                range, determineRangeUnit(field), symbol,
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(kLine.getTime())));
    }

    /**
     * Determines the range unit string based on the calendar field
     *
     * @param field The calendar field
     * @return The range unit string
     */
    private String determineRangeUnit(int field) {
        if (field == Calendar.MINUTE) {
            return "min";
        } else if (field == Calendar.HOUR_OF_DAY) {
            return "hour";
        } else if (field == Calendar.WEEK_OF_MONTH) {
            return "week";
        } else if (field == Calendar.DAY_OF_YEAR) {
            return "day";
        } else if (field == Calendar.MONTH) {
            return "month";
        }
        return "";
    }

    /**
     * A more efficient way to process weekly and monthly K-lines by aggregating daily K-lines
     *
     * @param kline    The K-line to populate
     * @param fromTime The start time
     * @param endTime  The end time
     * @param field    The calendar field
     */
    public void processKline(KLine kline, long fromTime, long endTime, int field) {
        if (service == null) {
            logger.warn("Cannot process K-line: MarketService is null for {}", symbol);
            return;
        }

        try {
            // Query the daily line of the past time period
            List<KLine> lines = service.findAllKLine(symbol, fromTime, endTime, "1day");

            if (lines.isEmpty()) {
                logger.debug("No daily K-lines found for {} between {} and {}",
                        symbol, new Date(fromTime), new Date(endTime));
                return;
            }

            // The opening price is set to the first day opening price
            kline.setOpenPrice(lines.get(0).getOpenPrice());
            kline.setLowestPrice(lines.get(0).getLowestPrice());

            // Aggregate data from all daily K-lines
            for (KLine item : lines) {
                // Update high and low prices
                kline.setHighestPrice(kline.getHighestPrice().max(item.getHighestPrice()));
                kline.setLowestPrice(kline.getLowestPrice().min(item.getLowestPrice()));

                // Accumulate volume, turnover, and count
                kline.setVolume(kline.getVolume().add(item.getVolume()));
                kline.setTurnover(kline.getTurnover().add(item.getTurnover()));
                kline.setCount(kline.getCount() + item.getCount());
            }

            // The closing price is set to the last day closing price
            kline.setClosePrice(lines.get(lines.size() - 1).getClosePrice());

            if (logger.isDebugEnabled()) {
                logger.debug("Processed {}-period K-line for {} with {} daily K-lines",
                        kline.getPeriod(), symbol, lines.size());
            }
        } catch (Exception e) {
            logger.error("Error processing K-line for {}: {}", symbol, e.getMessage(), e);
        }
    }

    /**
     * Gets the current K-line being processed
     *
     * @return The current K-line
     */
    @Override
    public KLine getKLine() {
        return currentKLine;
    }

    /**
     * Sets whether K-line generation should be stopped
     *
     * @param stop True to stop K-line generation, false to enable it
     */
    @Override
    public void setIsStopKLine(boolean stop) {
        this.stopKLine = stop;
    }

    /**
     * Checks if K-line generation is currently stopped
     *
     * @return True if K-line generation is stopped, false otherwise
     */
    @Override
    public boolean isStopKline() {
        return this.stopKLine;
    }
}
