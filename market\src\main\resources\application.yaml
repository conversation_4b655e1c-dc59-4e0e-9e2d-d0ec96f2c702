server:
  port: ${SERVER_PORT:6004}
  servlet:
    context-path: ${SERVER_CONTEXT_PATH:/market}

spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  application:
    name: market
    stream:
      kafka:
        binder:
          headers:
            - spanId
            - spanSampled
            - spanProcessId
            - spanParentSpanId
            - spanTraceId
            - spanName
            - messageSent
  redis:
    pool:
      max-active: 300
      max-wait: 60000
      max-idle: 100
      min-idle: 20
    timeout: 30000
  session:
    store-type: ${SPRING_SESSION_STORE_TYPE:none}
  datasource:
    driver-class-name: org.postgresql.Driver
    hikari:
      minimum-idle: 1
      maximum-pool-size: 2
      idle-timeout: 30000
      pool-name: HikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      auto-commit: true
      leak-detection-threshold: 2000
  jpa:
    database:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: false
    show-sql: ${SHOW_SQL:false}
    hibernate:
      ddl-auto: ${HIBERNATE_DDL_AUTO:none}
  devtools:
    restart:
      enabled: true

management:
  tracing:
    sampling:
      probability: 1.0
  endpoints:
    web:
      exposure:
        include: health,info,prometheus,metrics
  metrics:
    distribution:
      percentiles-histogram:
        http:
          server:
            requests: true
    tags:
      application: ${spring.application.name}

  health:
    elasticsearch:
      enabled: false
    mail:
      enabled: false
    redis:
      enabled: false # TODO need to remove later
  security:
    enabled: false
  context-path: /actuator

aqmd:
  netty:
    port: 28901
    bossThreadSize: 1
    worker-thread-size: 3
    packetHeaderLength: 4
    max-frame-length: **********
    writer-idle: 200
    max-timeout: 60
    defaultTimeout: 30
    deal-handler-thread-size: 10
    serviceLoggerLevel: debug
    direct-access-flag: 1
    direct-access-command: 20001,20002,20021,20022
    websocket-flag: 1
    websocket-port: 28985

es:
  username:
  password:
  mine:
    index:
    type:
  public:
    ip:
  private:
    ip: "#"
  port: 9200

endpoints:
  health:
    sensitive: false
    enabled: true
  info:
    sensitive: false
  metrics:
    sensitive: false


aliyun:
  mail-sms:
    region: ap-southeast-1
    access-key-id: LTAI5tSqZs8e1sMSBPzt3Dkm
    access-secret: ******************************
    from-address: <EMAIL>
    from-alias: BIZZAN
    sms-sign: BIZZAN
    sms-template: SMS_199285259
    email-tag: BIZZAN

spark:
  system:
    name: BIZZAN

second:
  referrer:
    award: false
openapi:
  service:
    api-docs: market
    server: http://localhost:8080
    title: API market Service
    version: 1.0.0

topic-kafka:
  minus:
    wallet-spot: minus-balance-wallet-spot

# Market configuration
market:
  kline:
    # Enable K-line generation by default (true = enable, false = wait for exchange service)
    enable-by-default: ${MARKET_KLINE_ENABLE_BY_DEFAULT:true}