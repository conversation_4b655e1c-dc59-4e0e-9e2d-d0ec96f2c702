-------------------------------------------------------------------------------
Test set: com.icetea.lotus.infrastructure.sharding.SymbolShardingManagerTest
-------------------------------------------------------------------------------
Tests run: 6, Failures: 0, Errors: 6, Skipped: 0, Time elapsed: 4.498 s <<< FAILURE! -- in com.icetea.lotus.infrastructure.sharding.SymbolShardingManagerTest
com.icetea.lotus.infrastructure.sharding.SymbolShardingManagerTest.testIsSymbolOwnedByThisPod_WhenSymbolOwnedByOtherPod_ShouldReturnFalse -- Time elapsed: 4.439 s <<< ERROR!
java.lang.NoClassDefFoundError: com/icetea/lotus/core/common/LogMessages$ShardingManager
	at com.icetea.lotus.infrastructure.sharding.SymbolShardingManager.<init>(SymbolShardingManager.java:74)
	at com.icetea.lotus.infrastructure.sharding.SymbolShardingManagerTest.setUp(SymbolShardingManagerTest.java:39)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: java.lang.ClassNotFoundException: com.icetea.lotus.core.common.LogMessages$ShardingManager
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526)
	... 5 more

com.icetea.lotus.infrastructure.sharding.SymbolShardingManagerTest.testIsSymbolOwnedByThisPod_WhenSymbolInConfigAndNotInRedis_ShouldAssignToThisPod -- Time elapsed: 0.008 s <<< ERROR!
java.lang.NoClassDefFoundError: com/icetea/lotus/core/common/LogMessages$ShardingManager
	at com.icetea.lotus.infrastructure.sharding.SymbolShardingManager.<init>(SymbolShardingManager.java:74)
	at com.icetea.lotus.infrastructure.sharding.SymbolShardingManagerTest.setUp(SymbolShardingManagerTest.java:39)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: java.lang.ClassNotFoundException: com.icetea.lotus.core.common.LogMessages$ShardingManager
	... 5 more

com.icetea.lotus.infrastructure.sharding.SymbolShardingManagerTest.testIsSymbolOwnedByThisPod_WhenSymbolInConfigButAlreadyAssignedToOtherPod_ShouldReturnFalse -- Time elapsed: 0.005 s <<< ERROR!
java.lang.NoClassDefFoundError: com/icetea/lotus/core/common/LogMessages$ShardingManager
	at com.icetea.lotus.infrastructure.sharding.SymbolShardingManager.<init>(SymbolShardingManager.java:74)
	at com.icetea.lotus.infrastructure.sharding.SymbolShardingManagerTest.setUp(SymbolShardingManagerTest.java:39)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: java.lang.ClassNotFoundException: com.icetea.lotus.core.common.LogMessages$ShardingManager
	... 5 more

com.icetea.lotus.infrastructure.sharding.SymbolShardingManagerTest.testIsSymbolOwnedByThisPod_WhenSymbolAlreadyOwnedByThisPod_ShouldReturnTrue -- Time elapsed: 0.003 s <<< ERROR!
java.lang.NoClassDefFoundError: com/icetea/lotus/core/common/LogMessages$ShardingManager
	at com.icetea.lotus.infrastructure.sharding.SymbolShardingManager.<init>(SymbolShardingManager.java:74)
	at com.icetea.lotus.infrastructure.sharding.SymbolShardingManagerTest.setUp(SymbolShardingManagerTest.java:39)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: java.lang.ClassNotFoundException: com.icetea.lotus.core.common.LogMessages$ShardingManager
	... 5 more

com.icetea.lotus.infrastructure.sharding.SymbolShardingManagerTest.testIsSymbolOwnedByThisPod_WhenAutoAssignDisabled_ShouldNotAssign -- Time elapsed: 0.004 s <<< ERROR!
java.lang.NoClassDefFoundError: com/icetea/lotus/core/common/LogMessages$ShardingManager
	at com.icetea.lotus.infrastructure.sharding.SymbolShardingManager.<init>(SymbolShardingManager.java:74)
	at com.icetea.lotus.infrastructure.sharding.SymbolShardingManagerTest.setUp(SymbolShardingManagerTest.java:39)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: java.lang.ClassNotFoundException: com.icetea.lotus.core.common.LogMessages$ShardingManager
	... 5 more

com.icetea.lotus.infrastructure.sharding.SymbolShardingManagerTest.testIsSymbolOwnedByThisPod_WhenSymbolNotInConfigAndNotInRedis_ShouldNotAssign -- Time elapsed: 0.003 s <<< ERROR!
java.lang.NoClassDefFoundError: com/icetea/lotus/core/common/LogMessages$ShardingManager
	at com.icetea.lotus.infrastructure.sharding.SymbolShardingManager.<init>(SymbolShardingManager.java:74)
	at com.icetea.lotus.infrastructure.sharding.SymbolShardingManagerTest.setUp(SymbolShardingManagerTest.java:39)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: java.lang.ClassNotFoundException: com.icetea.lotus.core.common.LogMessages$ShardingManager
	... 5 more

